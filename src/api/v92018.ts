import {
  Activity92018CreateOrUpdateRequest,
  Activity92018CreateOrUpdateResponse,
  Activity92018OrderRequest,
  Activity92018OrderResponse,
  Activity92018PartakeRecordPageRequest,
  Activity92018UserPrizeRecordPageRequest,
  BaseGetActivityRequest,
  BaseGetActivityResponse,
  IPageActivity92018PartakeRecordPageResponse,
  IPageActivity92018UserPrizeRecordPageResponse,
} from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 首购有礼
 * @summary 创建活动
 * @request POST:/92018/createActivity
 */
export const createActivity = (
  request: Activity92018CreateOrUpdateRequest,
): Promise<Activity92018CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92018/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼数据
 * @summary 订单查询
 * @request POST:/92018/data/getOrder
 */
export const dataGetOrder = (request: Activity92018OrderRequest): Promise<Activity92018OrderResponse[]> => {
  return httpRequest({
    url: '/92018/data/getOrder',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼数据
 * @summary 参与记录
 * @request POST:/92018/data/partakeLog
 */
export const dataPartakeLog = (
  request: Activity92018PartakeRecordPageRequest,
): Promise<IPageActivity92018PartakeRecordPageResponse> => {
  return httpRequest({
    url: '/92018/data/partakeLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼数据
 * @summary 参与记录导出
 * @request POST:/92018/data/partakeLog/export
 */
export const dataPartakeLogExport = (request: Activity92018PartakeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/92018/data/partakeLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼数据
 * @summary 参与记录人群包
 * @request POST:/92018/data/partakeLog/uploadPin
 */
export const dataPartakeLogUploadPin = (request: Activity92018PartakeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/92018/data/partakeLog/uploadPin',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼数据
 * @summary 中奖记录
 * @request POST:/92018/data/winningLog
 */
export const dataWinningLog = (
  request: Activity92018UserPrizeRecordPageRequest,
): Promise<IPageActivity92018UserPrizeRecordPageResponse> => {
  return httpRequest({
    url: '/92018/data/winningLog',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼数据
 * @summary 中奖记录导出
 * @request POST:/92018/data/winningLog/export
 */
export const dataWinningLogExport = (request: Activity92018UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/92018/data/winningLog/export',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼数据
 * @summary 中奖记录人群包
 * @request POST:/92018/data/winningLog/uploadPin
 */
export const dataWinningLogUploadPin = (request: Activity92018UserPrizeRecordPageRequest): Promise<void> => {
  return httpRequest({
    url: '/92018/data/winningLog/uploadPin',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼
 * @summary 查询活动信息
 * @request POST:/92018/getActivityInfo
 */
export const getActivityInfo = (request: BaseGetActivityRequest): Promise<BaseGetActivityResponse> => {
  return httpRequest({
    url: '/92018/getActivityInfo',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 首购有礼
 * @summary 修改活动
 * @request POST:/92018/updateActivity
 */
export const updateActivity = (
  request: Activity92018CreateOrUpdateRequest,
): Promise<Activity92018CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/92018/updateActivity',
    method: 'post',
    data: request,
  });
};
