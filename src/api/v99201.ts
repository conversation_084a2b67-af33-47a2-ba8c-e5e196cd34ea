import { Activity99201CreateOrUpdateRequest, Activity99201CreateOrUpdateResponse, DkpCommonDataRequest } from './types';

import { request as httpRequest } from 'ice';

/**
 * @tags 会员升级礼包
 * @summary 创建活动
 * @request POST:/99201/createActivity
 */
export const createActivity = (
  request: Activity99201CreateOrUpdateRequest,
): Promise<Activity99201CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99201/createActivity',
    method: 'post',
    data: request,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 互动数据
 * @request POST:/99201/data/actData
 */
export const dataActData = (joinLogRequest: DkpCommonDataRequest): Promise<object> => {
  return httpRequest({
    url: '/99201/data/actData',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 互动数据导出
 * @request POST:/99201/data/actData/export
 */
export const dataActDataExport = (joinLogRequest: DkpCommonDataRequest): Promise<void> => {
  return httpRequest({
    url: '/99201/data/actData/export',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 成交数据
 * @request POST:/99201/data/buyData
 */
export const dataBuyData = (joinLogRequest: DkpCommonDataRequest): Promise<object> => {
  return httpRequest({
    url: '/99201/data/buyData',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 成交数据导出
 * @request POST:/99201/data/buyData/export
 */
export const dataBuyDataExport = (joinLogRequest: DkpCommonDataRequest): Promise<void> => {
  return httpRequest({
    url: '/99201/data/buyData/export',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 领取记录
 * @request POST:/99201/data/prizeData
 */
export const dataPrizeData = (joinLogRequest: DkpCommonDataRequest): Promise<object> => {
  return httpRequest({
    url: '/99201/data/prizeData',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 领取记录导出
 * @request POST:/99201/data/prizeData/export
 */
export const dataPrizeDataExport = (joinLogRequest: DkpCommonDataRequest): Promise<void> => {
  return httpRequest({
    url: '/99201/data/prizeData/export',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 互动数据
 * @request POST:/99201/data/winningList
 */
export const dataWinningList = (joinLogRequest: DkpCommonDataRequest): Promise<object> => {
  return httpRequest({
    url: '/99201/data/winningList',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 互动数据导出
 * @request POST:/99201/data/winningList/export
 */
export const dataWinningListExport = (joinLogRequest: DkpCommonDataRequest): Promise<void> => {
  return httpRequest({
    url: '/99201/data/winningList/export',
    method: 'post',
    data: joinLogRequest,
  });
};

/**
 * @tags 会员升级礼包
 * @summary 修改活动
 * @request POST:/99201/updateActivity
 */
export const updateActivity = (
  request: Activity99201CreateOrUpdateRequest,
): Promise<Activity99201CreateOrUpdateResponse> => {
  return httpRequest({
    url: '/99201/updateActivity',
    method: 'post',
    data: request,
  });
};
