import React from 'react';
import LzTipPanel from '@/components/LzTipPanel';
import { Button, Divider, Input, Loading, Message, Table } from '@alifd/next';
import { importSkuText } from '@/api/sku';
import LzDialog from '@/components/LzDialog';

export default ({ uploadList, cancel, confirm, selectList, currentShopId }) => {
  const [skuIds, setSkuIds] = React.useState('');
  const [successLists, setSuccessLists] = React.useState<any[]>([]);
  const [errorLists, setErrorLists] = React.useState<any[]>([]);
  const [successLength, setSuccessLength] = React.useState(0);
  const [errorLength, setErrorLength] = React.useState(0);
  const [loading, setLoading] = React.useState(false);
  const [showDialog, setShowDialog] = React.useState(false);

  const saveSkuIds = async () => {
    if (!skuIds) {
      Message.error('请输入SKUID');
      return;
    }
    const newSkuIds = skuIds
      .replace(/[\x00-\x1f]+/g, ' ') // 去除特殊字符
      .replace(/^\D+|\D+$/g, '') // 去除头部数字之前和尾部数字之后的全部内容
      .replace(/(?<=\d)(?:(?!\d).)+(?=\d)/gs, ',') // 替换任意两组数字之间的全部内容为一个英文逗号
      .split(',');

    setLoading(true);
    const { errorList, successList } = await importSkuText({
      skuIdCount: newSkuIds as string[],
      shopId: currentShopId,
    });
    setLoading(false);
    uploadList(selectList.concat(successList || []));
    setSuccessLists(successList || []);
    setSuccessLength(successList?.length || 0);
    setErrorLists(errorList || []);
    setErrorLength(errorList?.length || 0);
  };

  return (
    <Loading visible={loading} style={{ width: '100%' }}>
      <LzTipPanel message={<span>使用导入功能，快速导入多个商品</span>} />
      <div>
        <Input.TextArea
          style={{ width: '100%' }}
          placeholder="每个SKU逗号分隔"
          aria-label="TextArea"
          rows={20}
          value={skuIds}
          onChange={(val) => setSkuIds(val)}
        />
      </div>
      <div style={{ margin: '10px 0' }}>
        成功导入 <span style={{ color: 'green' }}>{successLength}</span>个，失败{' '}
        <span style={{ color: 'red' }}>{errorLength}</span> 个
        <Button text type={'primary'} onClick={() => setShowDialog(true)}>
          失败原因
        </Button>
      </div>
      <Button type={'primary'} onClick={saveSkuIds}>
        确认导入
      </Button>

      <Divider />
      <div style={{ textAlign: 'right' }}>
        <Button
          type={'primary'}
          onClick={() => confirm(successLists)}
          style={{ marginRight: 8 }}
          disabled={successLists.length === 0}
        >
          确定
        </Button>
        <Button type={'secondary'} onClick={cancel}>
          取消
        </Button>
      </div>

      <LzDialog
        title={false}
        visible={showDialog}
        footer={false}
        onClose={() => setShowDialog(false)}
        style={{ width: '500px' }}
      >
        <Table dataSource={errorLists} style={{ marginTop: '15px' }}>
          <Table.Column title="SKUID" dataIndex="skuId" />
          <Table.Column title="失败原因" dataIndex="reason" width={'120px'} align={'center'} />
        </Table>
      </LzDialog>
    </Loading>
  );
};
