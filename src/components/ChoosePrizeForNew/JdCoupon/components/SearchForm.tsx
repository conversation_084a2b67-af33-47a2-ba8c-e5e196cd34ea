import * as React from 'react';
import { useState } from 'react';
import { Button, Form, Input, Select } from '@alifd/next';
import { isPopShop } from '@/utils';

const initBeanSearchData = {
  planName: '',
  couponIdOrPutKey: '',
  discountType: '',
  rangeType: '',
};

export default ({ onSearch }: any) => {
  const [beanSearchData, setBeanSearchData] = useState(initBeanSearchData);

  /**
   * 京豆筛选框赋值
   * @param {*} value 筛选值
   */
  const beanSelectData = (value: any) => {
    setBeanSearchData({
      ...beanSearchData,
      ...value,
    });
  };

  const onSearchClick = () => {
    onSearch(beanSearchData);
  };

  const onResetClick = () => {
    setBeanSearchData(initBeanSearchData);
    onSearch(initBeanSearchData);
  };

  return (
    <div>
      <Form inline>
        <Form.Item className="item" label="优惠券名称：">
          <Input
            value={beanSearchData.planName}
            className="dialog-search-ctrl"
            placeholder="请输入优惠券名称"
            onChange={(planName) => beanSelectData({ planName })}
          />
        </Form.Item>
        {isPopShop() && (
          <Form.Item className="item" label="优惠券ID：">
            <Input
              value={beanSearchData.couponIdOrPutKey}
              className="dialog-search-ctrl"
              placeholder="请输入优惠券ID"
              onChange={(couponIdOrPutKey) => beanSelectData({ couponIdOrPutKey })}
            />
          </Form.Item>
        )}
        {!isPopShop() && (
          <Form.Item className="item" label="优惠券ID/putKey：">
            <Input
              value={beanSearchData.couponIdOrPutKey}
              className="dialog-search-ctrl"
              placeholder="请输入优惠券ID/putKey"
              onChange={(couponIdOrPutKey) => beanSelectData({ couponIdOrPutKey })}
            />
          </Form.Item>
        )}
        <Form.Item className="item" label="优惠券方式：">
          <Select
            value={beanSearchData.discountType}
            className="dialog-search-ctrl"
            placeholder="请选择"
            onChange={(discountType) => beanSelectData({ discountType })}
          >
            <Select.Option value="1">满减</Select.Option>
            <Select.Option value="2">折扣</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item className="item" label="优惠券范围：">
          <Select
            value={beanSearchData.rangeType}
            placeholder="请选择"
            onChange={(rangeType) => beanSelectData({ rangeType })}
          >
            <Select.Option value="1">店铺券</Select.Option>
            <Select.Option value="2">商品券</Select.Option>
          </Select>
        </Form.Item>
        <Form.Item className="item" style={{ textAlign: 'right' }}>
          <Button type="primary" onClick={onSearchClick}>
            查询
          </Button>
          <Button style={{ marginLeft: 10 }} type="normal" onClick={onResetClick}>
            重置
          </Button>
        </Form.Item>
      </Form>
    </div>
  );
};
