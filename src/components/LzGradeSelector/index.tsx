import React, { forwardRef, useEffect, useState } from 'react';
import { Button, Checkbox, Divider, Icon, Overlay } from '@alifd/next';
import { getVenderLevelRule } from '@/api/common';
import styles from './index.module.scss';

const GradeSelector = forwardRef((props: any, _ref) => {
  const { value, onChange, disabled = false } = props;
  const [loading, setLoading] = useState(false);
  const [visible, setVisible] = useState(false);
  const [memberLevels, setMemberLevels] = useState([]);
  const [indeterminate, setIndeterminate] = React.useState(false);
  const [checkAll, setCheckAll] = React.useState(true);
  const [checkedList, setCheckedList] = React.useState([]);
  const [followChecked, setFollowChecked] = React.useState(false);
  const [curRef, setCurRef] = React.useState<any>();

  const emit = (list, follow) => {
    const nameList = list
      .sort((a, b) => {
        return a - b;
      })
      .map((id) => {
        const level: any = memberLevels.find((m: any) => m.customerLevel === id);
        return level?.customerLevelName || `${id}星会员`;
      });

    if (follow) {
      onChange([...list, -9].join(','), [...nameList, '关注店铺用户']);
    } else {
      onChange(list.join(','), nameList);
    }
  };

  const onCheckGroupChange = (list) => {
    console.log('🚀list🚀', list);
    console.log('🚀memberLevels🚀', memberLevels);
    list = list.filter((e) => e);
    setCheckedList(list);
    setIndeterminate(!!list.length && list.length < memberLevels.length);
    setCheckAll(list.length === memberLevels.length);
    emit(list, followChecked);
  };

  const onCheckAllChange = (checked, e) => {
    const clist: any = e.target.checked ? memberLevels.map((item: any) => item.customerLevel) : [];
    setCheckedList(clist);
    setIndeterminate(false);
    setCheckAll(e.target.checked);
    emit(clist, followChecked);
  };

  const onFollowerChange = (checked) => {
    setFollowChecked(checked);
    emit(
      checkedList.filter((e) => e),
      checked,
    );
  };

  const getData = () => {
    setLoading(true);
    getVenderLevelRule()
      .then((data) => {
        setMemberLevels(data as any);
        if (!data.length) {
          setCheckAll(false);
          setVisible(true);
        }
        const level = data.map((e) => e.customerLevel);
        level.push(-9);
        const name = data.map((e) => e.customerLevelName);
        name.push('关注店铺用户');
        onChange(level.join(','), name);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };

  useEffect(() => {
    getData();
  }, []);

  useEffect(() => {
    const values = value.split(',').map((v) => Number(v));
    setCheckedList(values.filter((v) => v !== -9).map((o) => Number(o)));
    setFollowChecked(values.some((v) => v === -9));
  }, [value]);

  return (
    <div>
      <Checkbox
        disabled={disabled}
        value={0}
        indeterminate={indeterminate}
        onChange={onCheckAllChange}
        checked={checkAll}
        ref={(ref) => {
          setCurRef(ref);
        }}
      >
        店铺会员
      </Checkbox>
      <Overlay v2 visible={visible} safeNode={() => curRef} target={() => curRef}>
        <div style={{ width: '500px' }}>
          <div className={[styles.tipPanel, styles.warnColor].join(' ')}>
            <i className={['iconfont', 'icon-icon-30', styles.icon].join(' ')} />
            <div>当前店铺暂未开通会员体系，需前往京东后台开通后再设置</div>
            <span>
              <Button
                text
                style={{ marginLeft: 15 }}
                type="primary"
                onClick={() => {
                  window.open('https://shop.jd.com/jdm/mkt/member/newmemberlevel', '_blank');
                }}
              >
                前往开通
              </Button>
            </span>
          </div>
        </div>
      </Overlay>
      <Checkbox.Group disabled={disabled} value={checkedList} onChange={onCheckGroupChange}>
        {memberLevels &&
          memberLevels.map((item: any) => (
            <Checkbox key={item.customerLevel} value={item.customerLevel}>
              {item.customerLevelName}
            </Checkbox>
          ))}
      </Checkbox.Group>
      {loading && <Icon type="loading" style={{ color: '#39f', marginLeft: '10px' }} />}
      <Divider dashed />
      <Checkbox disabled={disabled} value={-9} checked={followChecked} onChange={onFollowerChange}>
        关注店铺用户
      </Checkbox>
    </div>
  );
});

export default GradeSelector;
