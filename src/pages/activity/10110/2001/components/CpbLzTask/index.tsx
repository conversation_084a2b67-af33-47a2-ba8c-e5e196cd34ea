/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-05 11:08
 * Description:
 */
import React, { useState, useEffect } from 'react';
import styles from './index.module.scss';
import { Form, Select } from '@alifd/next';
// 各任务组件
// 数据流同之前，仔细阅读注释即可
import InCart from './components/InCart';
import ShareActivity from './components/ShareActivity';
import BrowseStore from './components/BrowseStore';
import BrowseItem from './components/BrowseItem';

const formItemLayout: any = {
  labelAlign: 'top',
  colon: true,
};
interface SelectOptions {
  label: string;
  value: number;
}

const ACT_TYPE: SelectOptions[] = [
  { label: '浏览店铺', value: 2 },
  { label: '浏览商品', value: 3 },
  { label: '加购商品', value: 7 },
  { label: '分享活动', value: 12 },
];
const types = ACT_TYPE.map((item) => item.value);
/**
 * @description 选择任务Dialog
 * @param onCancel  取消事件
 * @param onSubmit  提交事件（任务信息）
 * @param editValue 修改数据
 * @param typeList  需要的任务类型集合
 */
interface TaskFormProps {
  onCancel: () => void;
  onSubmit: (taskInfo: any) => void;
  editValue?: any;
  typeList?: number[];
  formData: any;
}
export default ({ onCancel, onSubmit, editValue, typeList = types, formData }: TaskFormProps) => {
  const [selectValue, setSelectValue] = useState<number>(typeList[0]);
  const itemRender = (item: { label: string }) => {
    const { label } = item;
    return (
      <>
        <span>{label}</span>
      </>
    );
  };
  useEffect(() => {
    // 获取活动类型，给actTypes赋值，取0项作为默认值
    setSelectValue((editValue && editValue.taskType) || typeList[0]);
  }, []);

  const TaskProps = {
    editValue,
    onSubmit,
    onCancel,
    formData,
  };

  const TaskComponents = {
    // 浏览店铺
    2: <BrowseStore {...TaskProps} />,
    // 浏览商品
    3: <BrowseItem {...TaskProps} />,
    // 加购商品
    7: <InCart {...TaskProps} />,
    // 分享活动
    12: <ShareActivity {...TaskProps} />,
  };
  return (
    <div className={styles.lzTask}>
      <Form {...formItemLayout}>
        <Form.Item label="任务类型">
          <Select
            style={{ width: '100%' }}
            dataSource={ACT_TYPE.filter((item) => typeList.includes(item.value))}
            disabled={!!editValue}
            value={selectValue}
            onChange={(val) => setSelectValue(val)}
            itemRender={itemRender}
          />
        </Form.Item>
      </Form>
      {selectValue && TaskComponents[selectValue]}
    </div>
  );
};
