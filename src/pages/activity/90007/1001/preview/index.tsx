/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer, useState } from 'react';
import { Button, Form, Input, Table } from '@alifd/next';
import { formItemLayout, PageData, PRIZE_TYPE } from '../util';
import styles from './style.module.scss';
import { goToPropertyList } from '@/utils';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const [skuVisible, setSkuVisible] = useState(false);
  const [seriesSkuList, setSeriesSkuList] = useState([]);
  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">{formData.threshold === 0 ? '无门槛' : '店铺会员'}</FormItem>
        {formData.seriesList.length > 0 &&
          formData.seriesList.map((item) => {
            return (
              <>
                <FormItem label="系列名">{item.seriesName}</FormItem>
                <FormItem label="系列背景图">
                  <img src={item.seriesPic} style={{ width: '150px' }} />
                </FormItem>
                <FormItem label="链接">{item.seriesUrl}</FormItem>
                <FormItem label="SKU列表">
                  <Button
                    onClick={() => {
                      console.log(item.seriesSkuList);
                      setSeriesSkuList(item.seriesSkuList);
                      setSkuVisible(true);
                    }}
                  >
                    查看SKU
                  </Button>
                </FormItem>
                <FormItem label="奖品列表">
                  <Table dataSource={item.seriesPrizeList} style={{ marginTop: '15px' }}>
                    <Table.Column title={'罐数'} cell={(_, index, row) => <div>{row.potNum}罐</div>} />
                    <Table.Column title="奖品名称" dataIndex="prizeName" />
                    <Table.Column
                      title="奖品类型"
                      cell={(_, index, row) => (
                        <div style={{ cursor: 'pointer' }} onClick={() => goToPropertyList(row.prizeType)}>
                          {PRIZE_TYPE[row.prizeType]}
                        </div>
                      )}
                    />
                    <Table.Column
                      title="单位数量"
                      cell={(_, index, row) => {
                        if (row.prizeType === 1) {
                          return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                        } else {
                          return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                        }
                      }}
                    />
                    <Table.Column
                      title="发放份数"
                      cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                    />
                    <Table.Column
                      title="单份价值(元)"
                      cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : '0'}</div>}
                    />
                    <Table.Column
                      title="奖品图"
                      cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                    />
                  </Table>
                </FormItem>
              </>
            );
          })}
        <FormItem label="订单限制">限制</FormItem>
        <FormItem label="下单时间">{`${format.formatDateTimeDayjs(
          formData.orderRangeDate[0],
        )}至${format.formatDateTimeDayjs(formData.orderRangeDate[1])}`}</FormItem>
        <FormItem label="订单状态">已完成</FormItem>
        <FormItem label="不同系列奖励是否叠享">
          {formData.diffSeriesReceiveLimit
            ? '叠加享受（用户领取了系列A的集罐奖励，还可继续参与领取系列B、系列C...的集罐奖励）'
            : '不叠加享受（用户领了该系列的集罐奖励，则无法参与其他系列集罐奖励）'}
        </FormItem>
        <FormItem label="同系列奖励是否叠享">
          {formData.sameSeriesReceiveLimit
            ? '叠加享受（用户领取了系列A的集3罐奖励，还可继续参与领取系列A的集6罐奖励、12罐...奖励）'
            : '不叠加享受（用户领了系列A的集3罐奖励，则无法领取系列A的6罐奖）'}
        </FormItem>
        <FormItem label="每人每种奖品最多兑换">
          {formData.perReceiveLimit ? `限制${formData.perReceiveCount}个` : '不限制'}
        </FormItem>
        <FormItem label="领取时间">
          {formData.receiveTimeType === 0
            ? '活动时间'
            : `指定时间${format.formatDateTimeDayjs(formData.receivePointRangeDate[0])}至${format.formatDateTimeDayjs(
                formData.receivePointRangeDate[1],
              )}`}
        </FormItem>
        <FormItem label="是否添加曝光商品">
          {formData.isExposure === 0 && <div>否</div>}
          {formData.isExposure === 1 && (
            <div className={styles.container}>
              {formData.exposureSkuList?.map((sku, index) => {
                return (
                  <div className={styles.skuContainer}>
                    <img className={styles.skuImg} src={sku.skuMainPicture} alt="" />
                    <div>
                      <div className={styles.skuName}>{sku.skuName}</div>
                      <div className={styles.skuId}>SKUID:{sku.skuId}</div>
                      <div className={styles.price}>¥ {sku.jdPrice}</div>
                    </div>
                  </div>
                );
              })}
            </div>
          )}
        </FormItem>
        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        <FormItem label="规则内容">
          <Input.TextArea className="rule-word-break" value={formData.rules} />
        </FormItem>
      </Form>
      {/*  sku列表 */}
      <LzDialog
        title={false}
        visible={skuVisible}
        footer={false}
        onClose={() => setSkuVisible(false)}
        style={{ width: '670px', height: '500' }}
      >
        {seriesSkuList.length && (
          <div style={{ margin: '10px' }}>
            <Table dataSource={seriesSkuList} fixedHeader>
              <Table.Column title="	罐数" dataIndex="potNum" />
              <Table.Column title="商品id" dataIndex="skuId" />
            </Table>
          </div>
        )}
      </LzDialog>
    </div>
  );
};
