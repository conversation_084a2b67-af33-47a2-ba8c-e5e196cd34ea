import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Select, Table } from '@alifd/next';
import constant from '@/utils/constant';
import LzPagination from '@/components/LzPagination';
import { dataSignUpLog, dataSignUpLogExport, dataSignUpLogUploadsignUpPin } from '@/api/v90021';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import format from '@/utils/format';
import LzDialog from '@/components/LzDialog';
import LzGenerateCrowdBag from '@/components/LzGenerateCrowdBag';

import dayjs from 'dayjs';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const { Option } = Select;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [orderFileList, setOrderFileList] = useState<File[]>([]);
  const [uploaderRef, setUploaderRef] = useState(false);
  const saveUploaderRef = (ref) => {
    if (!ref) {
      return;
    }
    setUploaderRef(ref.getInstance());
  };

  const defaultRangeVal = [
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD 00:00:00'),
    dayjs().format('YYYY-MM-DD 23:59:59'),
  ];

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  // const onRowSelectionChange = (selectKey: string[]): void => {
  //   console.log(selectKey);
  // };
  // const rowSelection: {
  //   mode: 'single' | 'multiple' | undefined;
  //   onChange: (selectKey: string[]) => void;
  // } = {
  //   mode: 'multiple',
  //   onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  // };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataSignUpLog(query)
      .then((res: any): void => {
        setTableData(res.records as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.size!;
        pageInfo.pageNum = +res.current!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataSignUpLogExport(formValue).then((data: any) => downloadExcel(data, '全渠道0元试喝报名记录'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <Form.Item name="nickName" label="用户昵称">
          <Input maxLength={20} placeholder="请输入用户昵称" />
        </Form.Item>
        <Form.Item name="pin" label="用户PIN">
          <Input placeholder="请输入用户PIN" />
        </Form.Item>
        <Form.Item name="xid" label="Xid">
          <Input placeholder="请输入Xid" />
        </Form.Item>
        <Form.Item name="mobile" label="手机号">
          <Input placeholder="请输入手机号" />
        </Form.Item>
        <FormItem name="dateRange" label="报名时间">
          <RangePicker
            showTime
            hasClear={false}
            defaultValue={defaultRangeVal}
            format={constant.DATE_FORMAT_TEMPLATE}
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
        <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}>
          生成人群包
        </Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        {/* <Table.Column width={80} title="序号" dataIndex="num" /> */}
        <Table.Column
          title="用户昵称"
          width={100}
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.nickName ? Utils.mask(row.nickName) : '-'}
                  {row.nickName && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.nickName).then(() => {
                          Message.success('用户昵称已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="用户PIN"
          width={100}
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.pin ? Utils.mask(row.pin) : '-'}
                  {row.pin && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.pin).then(() => {
                          Message.success('用户PIN已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column
          title="Xid"
          cell={(_, index, row) => (
            <div>
              {
                <div className="lz-copy-text">
                  {row.xid}
                  {row.xid && (
                    <span
                      className={'iconfont icon-fuzhi'}
                      style={{ marginLeft: 5 }}
                      onClick={() => {
                        Utils.copyText(row.xid).then(() => {
                          Message.success('用户Xid已复制到剪切板');
                        });
                      }}
                    />
                  )}
                </div>
              }
            </div>
          )}
        />
        <Table.Column title="手机号" dataIndex="mobile" />
        <Table.Column
          title="报名时间"
          dataIndex="createTime"
          cell={(value, index, data) => <div>{format.formatDateTimeDayjs(data.createTime)}</div>}
        />
      </Table>
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      <LzDialog
        title="生成人群包"
        className="lz-dialog-mini"
        visible={packVisible}
        footer={false}
        onCancel={() => setPackVisible(false)}
        onClose={() => setPackVisible(false)}
      >
        <LzGenerateCrowdBag
          dataUploadPin={dataSignUpLogUploadsignUpPin}
          formValue={field.getValues()}
          cancel={() => setPackVisible(false)}
        />
      </LzDialog>
    </div>
  );
};
