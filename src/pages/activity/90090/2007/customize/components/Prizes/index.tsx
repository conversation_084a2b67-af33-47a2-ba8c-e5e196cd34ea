/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-05-29 18:02
 * Description: 大转盘
 */
import React, { useEffect, useReducer, useState } from 'react';
import styles from './style.module.scss';
import { Button, Form, Grid, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';
import LzDialog from '@/components/LzDialog';
import EditHotZone from '../EditHotZone';
import { deepCopy } from '@/utils';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 9,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [hotVisible, setHotVisible] = React.useState(false);
  const [editHotData, setEditHotData] = useState({
    bg: '',
    hotZoneList: [],
  });
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };

  /**
   * 添加热区
   */
  const addHotZone = () => {
    if (!formData.prizeBg) {
      Message.error('请先上传背景图片后添加热区');
      return;
    }
    setEditHotData({
      bg: formData.prizeBg,
      hotZoneList: formData.hotZoneList,
    });
    setHotVisible(true);
  };

  const onCloseHotZone = () => {
    setHotVisible(false);
  };

  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel title="领奖设置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="会员复购限定特权选择背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={717}
                  height={683}
                  value={formData.equitySelection}
                  onChange={(equitySelection) => {
                    setForm({ equitySelection });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度717px，推荐高度683px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ equitySelection: defaultValue?.equitySelection });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="资格提示/选购提示框">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={670}
                  height={89}
                  value={formData.qualificationBg}
                  onChange={(qualificationBg) => {
                    setForm({ qualificationBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：670px*89px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ qualificationBg: defaultValue?.qualificationBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="已完成Icon">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={88}
                  height={43}
                  value={formData.orderFinishIcon}
                  onChange={(orderFinishIcon) => {
                    setForm({ orderFinishIcon });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：88px*43px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ orderFinishIcon: defaultValue?.orderFinishIcon });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="前置订单背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={270}
                  height={58}
                  value={formData.preOrderBg}
                  onChange={(preOrderBg) => {
                    setForm({ preOrderBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：270px*58px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ preOrderBg: defaultValue?.preOrderBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="后置订单背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={270}
                  height={58}
                  value={formData.postOrderBg}
                  onChange={(postOrderBg) => {
                    setForm({ postOrderBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：270px*58px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ postOrderBg: defaultValue?.postOrderBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="订单时间颜色">
            <LzColorPicker value={formData.orderTimeColor} onChange={(orderTimeColor) => setForm({ orderTimeColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ orderTimeColor: defaultValue?.orderTimeColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="前后订单中间Icon">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={70}
                  height={70}
                  value={formData.orderIcon}
                  onChange={(orderIcon) => {
                    setForm({ orderIcon });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：70px*70px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ orderIcon: defaultValue?.orderIcon });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="未达标Icon">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={130}
                  height={100}
                  value={formData.orderUnfinishedIcon}
                  onChange={(orderUnfinishedIcon) => {
                    setForm({ orderUnfinishedIcon });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：130px*100px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ orderUnfinishedIcon: defaultValue?.orderUnfinishedIcon });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="资格提示字体颜色">
            <LzColorPicker
              value={formData.qualificationTextColor}
              onChange={(qualificationTextColor) => setForm({ qualificationTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ qualificationTextColor: defaultValue?.qualificationTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          {/* <Form.Item label="订单限制提示字体颜色">
            <LzColorPicker
              value={formData.orderLimitTextColor}
              onChange={(orderLimitTextColor) => setForm({ orderLimitTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ orderLimitTextColor: defaultValue?.orderLimitTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item> */}
          <Form.Item label="进度条背景框">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={665}
                  height={216}
                  value={formData.progressBarBg}
                  onChange={(progressBarBg) => {
                    setForm({ progressBarBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：665px*216px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ progressBarBg: defaultValue?.progressBarBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="进度条上方字体颜色">
            <LzColorPicker value={formData.topTextColor} onChange={(topTextColor) => setForm({ topTextColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ topTextColor: defaultValue?.topTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="进度条下方字体颜色">
            <LzColorPicker
              value={formData.bottomTextColor}
              onChange={(bottomTextColor) => setForm({ bottomTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ bottomTextColor: defaultValue?.bottomTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="领奖背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={717}
                  // height={2360}
                  value={formData.prizeBg}
                  onChange={(prizeBg) => {
                    setForm({ prizeBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度717px，建议高度1690px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ prizeBg: defaultValue?.prizeBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="限时惊喜福利背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={665}
                  height={278}
                  value={formData.timeLimitedPrizeBg}
                  onChange={(timeLimitedPrizeBg) => {
                    setForm({ timeLimitedPrizeBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：665px*278px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ timeLimitedPrizeBg: defaultValue?.timeLimitedPrizeBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="限时惊喜福利字体颜色">
            <LzColorPicker
              value={formData.timeLimitedTextColor}
              onChange={(timeLimitedTextColor) => setForm({ timeLimitedTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ timeLimitedTextColor: defaultValue?.timeLimitedTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="限时惊喜福利区按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={102}
                  height={102}
                  value={formData.timeLimitedBtnBg}
                  onChange={(timeLimitedBtnBg) => {
                    setForm({ timeLimitedBtnBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：102px*102px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ timeLimitedBtnBg: defaultValue?.timeLimitedBtnBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="限时惊喜福利按钮字体颜色">
            <LzColorPicker
              value={formData.timeLimitedBtnTextColor}
              onChange={(timeLimitedBtnTextColor) => setForm({ timeLimitedBtnTextColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ timeLimitedBtnTextColor: defaultValue?.timeLimitedBtnTextColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="n选m区域标题字体颜色">
            <LzColorPicker
              value={formData.choosePrizeTitleColor}
              onChange={(choosePrizeTitleColor) => setForm({ choosePrizeTitleColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ choosePrizeTitleColor: defaultValue?.choosePrizeTitleColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="n选m区域单个礼品背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={220}
                  height={345}
                  value={formData.prizeItemBg}
                  onChange={(prizeItemBg) => {
                    setForm({ prizeItemBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：220px*345px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ prizeItemBg: defaultValue?.prizeItemBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="n选m区域单个礼品标题背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={220}
                  height={60}
                  value={formData.prizeItemTitleBg}
                  onChange={(prizeItemTitleBg) => {
                    setForm({ prizeItemTitleBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：220px*60px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ prizeItemTitleBg: defaultValue?.prizeItemTitleBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="n选m区域单个礼品标题颜色">
            <LzColorPicker
              value={formData.prizeItemTitleColor}
              onChange={(prizeItemTitleColor) => setForm({ prizeItemTitleColor })}
            />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ prizeItemTitleColor: defaultValue?.prizeItemTitleColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="n选m区域领取奖品按钮背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={162}
                  height={43}
                  value={formData.getPrizeBtn}
                  onChange={(getPrizeBtn) => {
                    setForm({ getPrizeBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：162px*43px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ getPrizeBtn: defaultValue?.getPrizeBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="n选m区域领取奖品兑完按钮"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={162} */}
          {/*        height={43} */}
          {/*        value={formData.noMorePrizeBtn} */}
          {/*        onChange={(noMorePrizeBtn) => { */}
          {/*          setForm({ noMorePrizeBtn }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：162px*43px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：JPG、JPEG、PNG、GIF</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ noMorePrizeBtn: defaultValue?.noMorePrizeBtn }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
          {/* <Form.Item label="图片热区" required key="hot"> */}
          {/*  <Button */}
          {/*    style={{ margin: '20px auto', width: '20%', color: '#3399FF' }} */}
          {/*    type="normal" */}
          {/*    onClick={() => { */}
          {/*      addHotZone(); */}
          {/*    }} */}
          {/*  > */}
          {/*    <Icon className="iconfont icon-a-1plus" style={{ marginRight: 10 }} /> */}
          {/*    {`添加热区`} */}
          {/*  </Button> */}
          {/* </Form.Item> */}
          <LzDialog
            title={'编辑热区'}
            visible={hotVisible}
            footer={false}
            onClose={() => setHotVisible(false)}
            style={{ width: '750px' }}
          >
            <EditHotZone
              data={deepCopy(editHotData)}
              dataIndex={0}
              dispatch={(val: any) => {
                setForm({
                  hotZoneList: val,
                });
                setHotVisible(false);
              }}
              onClose={() => onCloseHotZone()}
            />
          </LzDialog>
        </Form>
      </LzPanel>
    </div>
  );
};
