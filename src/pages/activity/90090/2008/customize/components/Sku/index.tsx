/**
 * Author: z<PERSON><PERSON>e
 * Date: 2023-05-29 18:02
 * Description: 获奖名单
 */
import React, { useEffect, useReducer } from 'react';
import styles from './style.module.scss';
import { Button, Form, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '@/pages/activity/90090/1003/util';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'labelAlign' | 'wrapperCol'> = {
  labelCol: {
    fixedSpan: 9,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);

  return (
    <div className={styles.award}>
      <LzPanel title="曝光商品">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="曝光商品图片">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.showSkuBg}
                  onChange={(showSkuBg) => {
                    setForm({ showSkuBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px*推荐高度3138px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ showSkuBg: defaultValue?.showSkuBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="价格颜色">
            <LzColorPicker value={formData.priceColor} onChange={(priceColor) => setForm({ priceColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ priceColor: defaultValue?.priceColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
