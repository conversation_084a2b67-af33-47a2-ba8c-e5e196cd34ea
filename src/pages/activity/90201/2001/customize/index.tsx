import React from 'react';
import { CustomValue } from '../util';
// 基础信息
import Base from './components/Base';
import Member from './components/Member';
import Rule from './components/Rule';
import NotSaveAddress from './components/NotSaveAddress';
import SaveAddress from './components/SaveAddress';
import ConfirmAddress from './components/ConfirmAddress';
import AddressInfo from './components/AddressInfo';
import MyPrize from './components/MyPrize';
import ConfirmPrize from './components/ConfirmPrize';
import { Tab } from '@alifd/next';

interface Props {
  target: number;
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  handleChange: (formData: Required<CustomValue>) => void;
  handleChangeActiveKey: (activeKey: string) => void;
}

interface EventProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<CustomValue>) => void;
}

export default (props: Props) => {
  const { target, defaultValue, value, handleChange } = props;
  const [popupActiveKey, setPopupActiveKey] = React.useState('index');
  const handleTabChange = (key: string): void => {
    setPopupActiveKey(key);
    props.handleChangeActiveKey(key);
  };
  console.log('target', target);
  // 各装修自组件抛出的数据
  // 只负责转发，不作处理
  const onChange = (formData: any): void => {
    handleChange(formData);
  };
  const eventProps: EventProps = {
    defaultValue,
    value,
    onChange,
  };
  return (
    <div>
      <Tab activeKey={popupActiveKey} defaultActiveKey="1" onChange={handleTabChange}>
        <Tab.Item title="主页" key="index">
          <Base {...eventProps} />
        </Tab.Item>
        <Tab.Item title="入会弹窗" key="member">
          <Member {...eventProps} />
        </Tab.Item>
        <Tab.Item title="规则弹窗" key="rule">
          <Rule {...eventProps} />
        </Tab.Item>
        {/* <Tab.Item title="活动商品弹窗" key="goods">
          <Goods {...eventProps} />
        </Tab.Item> */}
        <Tab.Item title="确认领取弹窗" key="confirm">
          <ConfirmPrize {...eventProps} />
        </Tab.Item>
        <Tab.Item title="收货及物流信息弹窗" key="addressInfo">
          <AddressInfo {...eventProps} />
        </Tab.Item>
        <Tab.Item title="我的奖品弹窗" key="myPrize">
          <MyPrize {...eventProps} />
        </Tab.Item>
        <Tab.Item title="未保存地址弹窗" key="notSaveAddress">
          <NotSaveAddress {...eventProps} />
        </Tab.Item>
        <Tab.Item title="保存地址弹窗" key="saveAddress">
          <SaveAddress {...eventProps} />
        </Tab.Item>
        <Tab.Item title="确认地址弹窗" key="confirmAddress">
          <ConfirmAddress {...eventProps} />
        </Tab.Item>
      </Tab>
    </div>
  );
};
