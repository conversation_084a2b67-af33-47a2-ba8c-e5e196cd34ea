import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { deepCopy, downloadExcel, getParams } from '@/utils';
import { reportPageNew, reportPageNewExport } from '@/api/v90202Data';

import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;

const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
const STATUS = [
  { label: '全部', value: '' },
  { label: '失败', value: 0 },
  { label: '成功', value: 1 },
];

const defaultTableColumns = () => {
  return [
    {
      title: '活动日期',
      dataIndex: 'activityTime',
      lock: true,
      width: 120,
    },
    {
      title: '总数据',
      children: [
        {
          title: 'PV',
          dataIndex: 'pv',
          width: 90,
        },
        {
          title: 'UV',
          dataIndex: 'uv',
          width: 90,
        },
        {
          title: '付款人数',
          dataIndex: 'payUser',
          width: 90,
        },
        {
          title: '付款金额',
          dataIndex: 'payAmount',
          width: 90,
        },
        {
          title: '开卡人数',
          dataIndex: 'openCardUser',
          width: 90,
        },
        {
          title: '领取人数',
          dataIndex: 'receiveUser',
          width: 90,
        },
      ],
    },
  ];
};
export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableCloumns, setTableColumns] = useState<any[]>(defaultTableColumns());
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const defaultRangeVal = [
    dayjs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayjs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ];
  const [packVisible, setPackVisible] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };
  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.startDate = dayjs(query.dateRange[0]).format('YYYY-MM-DD');
    query.endDate = dayjs(query.dateRange[1]).format('YYYY-MM-DD');
    reportPageNew(query)
      .then((data): void => {
        if (data[0]) {
          const columns = [];
          data[0].stepDataResponseList.forEach((item) => {
            const colmIndex = columns.findIndex((col) => col.title === item.seriesName);
            const stepColum = {
              title: item.stepName,
              stepSort: item.stepSort,
              children: [
                {
                  title: '领取人数',
                  dataIndex: `receiveUser${item.stepId}`,
                  width: 90,
                },
                {
                  title: '付款人数',
                  dataIndex: `payUser${item.stepId}`,
                  width: 90,
                },
                {
                  title: '付款金额',
                  dataIndex: `payAmount${item.stepId}`,
                  width: 90,
                },
              ],
            };
            if (colmIndex === -1) {
              columns.push({
                title: item.seriesName,
                seriesSort: item.seriesSort,
                children: [stepColum],
              });
            } else {
              columns[colmIndex].children.push(stepColum);
            }
          });
          columns.sort((a, b) => a.seriesSort - b.seriesSort);
          columns.forEach((item) => {
            item.children.sort((a, b) => a.stepSort - b.stepSort);
          });
          setTableColumns([...defaultTableColumns(), ...columns]);
        }
        data.forEach((item) => {
          item.stepDataResponseList.forEach((step) => {
            item[`receiveUser${step.stepId}`] = step.receiveUser;
            item[`payUser${step.stepId}`] = step.payUser;
            item[`payAmount${step.stepId}`] = step.payAmount;
          });
        });
        setTableData(data);
        // pageInfo.total = +res.total!;
        // pageInfo.pageSize = +res.size!;
        // pageInfo.pageNum = +res.current!;
        // setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };

  const exportData = () => {
    const query = field.getValues();
    query.activityId = getParams('id');
    query.startDate = dayjs(query.dateRange[0]).format('YYYY-MM-DD');
    query.endDate = dayjs(query.dateRange[1]).format('YYYY-MM-DD');
    reportPageNewExport(query).then((data: any) => downloadExcel(data, '满额有礼活动数据'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div style={{ minHeight: '350px' }}>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="时间范围">
          <RangePicker hasClear={false} defaultValue={defaultRangeVal} format={'YYYY-MM-DD'} />
        </FormItem>
        {/* <Form.Item name="pin" label="用户pin"> */}
        {/*   <Input placeholder="请输入用户pin" /> */}
        {/* </Form.Item> */}
        {/* <Form.Item name="status" label="状态"> */}
        {/*   <Select followTrigger mode="single" defaultValue="" style={{ marginRight: 8 }} dataSource={STATUS} /> */}
        {/* </Form.Item> */}
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
          <Button onClick={exportData}>导出</Button>
        </FormItem>
      </Form>
      {/* <div style={{ margin: '10px 0', textAlign: 'right' }}> */}
      {/*   <Button disabled={!tableData.length} onClick={() => setPackVisible(true)} style={{ marginLeft: '10px' }}> */}
      {/*     生成人群包 */}
      {/*   </Button> */}
      {/* </div> */}
      <Table columns={tableCloumns} dataSource={tableData} loading={loading} />
      <LzPagination
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        total={pageInfo.total}
        onChange={handlePage}
      />
      {/* <LzDialog */}
      {/*   title="生成人群包" */}
      {/*   className="lz-dialog-mini" */}
      {/*   visible={packVisible} */}
      {/*   footer={false} */}
      {/*   onCancel={() => setPackVisible(false)} */}
      {/*   onClose={() => setPackVisible(false)} */}
      {/* > */}
      {/*   <LzGenerateCrowdBag */}
      {/*     dataUploadPin={userPrizeRecordPageUploadPin} */}
      {/*     formValue={field.getValues()} */}
      {/*     cancel={() => setPackVisible(false)} */}
      {/*   /> */}
      {/* </LzDialog> */}
    </div>
  );
};
