/**
 * Author: zhang<PERSON>e
 * Date: 2023-05-29 18:02
 * Description: 获奖名单
 */
import React, { useEffect, useReducer } from 'react';
import styles from './style.module.scss';
import { Button, Form, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '@/pages/activity/91010/2001/util';
import { deepCopy } from '@/utils';

const formLayout: Omit<FormLayout, 'labelAlign' | 'wrapperCol'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  const addSkuClick = () => {
    console.log('addSkuClick');
    formData.skuListDeco.push({
      skuImg: '',
      position: formData.skuListDeco + 1,
    });
    setForm(formData);
  };
  return (
    <div className={styles.award}>
      <LzPanel title="活动页面设置">
        <Form {...formLayout} className={styles.form}>
          {formData.skuListDeco &&
            formData.skuListDeco.length > 0 &&
            formData.skuListDeco.map((item, index) => {
              return (
                <Form.Item key={index} label={`商品图片${index + 1}`}>
                  <Grid.Row>
                    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                      <LzImageSelector
                        className={styles.skuImgDiv}
                        width={650}
                        height={190}
                        value={item.skuImg}
                        onChange={(skuImg) => {
                          formData.skuListDeco[index].skuImg = skuImg;
                          setForm(formData);
                        }}
                      />
                    </Form.Item>
                    <Form.Item style={{ marginBottom: 0 }}>
                      <div className={styles.tip}>
                        <p>图片尺寸：650*190px</p>
                        <p>图片大小：不超过1M</p>
                        <p>图片格式：JPG、JPEG、PNG</p>
                      </div>
                      <div>
                        <Button
                          type="primary"
                          text
                          onClick={() => {
                            formData.skuListDeco.splice(index, 1);
                            setForm(formData);
                          }}
                        >
                          删除
                        </Button>
                        <Button
                          type="primary"
                          text
                          onClick={() => {
                            const defaultValue1 = deepCopy(defaultValue?.skuListDeco[index]);
                            formData.skuListDeco[index].skuImg = defaultValue1?.skuImg;
                            setForm(formData);
                          }}
                        >
                          重置
                        </Button>
                        {index !== 0 && (
                          <Button
                            type="primary"
                            text
                            onClick={() => {
                              formData.skuListDeco.splice(
                                index - 1,
                                1,
                                ...formData.skuListDeco.splice(index, 1, formData.skuListDeco[index - 1]),
                              );
                              setForm(formData);
                            }}
                          >
                            上移
                          </Button>
                        )}
                        {index !== formData.skuListDeco.length - 1 && (
                          <Button
                            type="primary"
                            text
                            onClick={() => {
                              formData.skuListDeco.splice(
                                index,
                                1,
                                ...formData.skuListDeco.splice(index + 1, 1, formData.skuListDeco[index]),
                              );
                              setForm(formData);
                            }}
                          >
                            下移
                          </Button>
                        )}
                      </div>
                    </Form.Item>
                  </Grid.Row>
                </Form.Item>
              );
            })}
          <Form.Item label={' '} colon={false}>
            <Button
              type="primary"
              onClick={() => {
                addSkuClick();
              }}
            >
              新增商品图片
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
