/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-06-07 09:50
 * Description: 活动预览
 */
import React, { useReducer } from 'react';
import { Form, Table } from '@alifd/next';
import { formItemLayout, PageData } from '../util';
import styles from './style.module.scss';
import format from '@/utils/format';

const FormItem = Form.Item;

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  labelAlign?: 'left' | 'top';
}

export default ({ defaultValue, value, labelAlign = 'left' }: Props) => {
  const [formData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  formItemLayout.labelAlign = labelAlign;
  return (
    <div className={styles.preview}>
      <Form {...formItemLayout} isPreview>
        <FormItem label="活动标题">{formData.activityName}</FormItem>
        <FormItem label="活动时间">{`${format.formatDateTimeDayjs(formData.rangeDate[0])}至${format.formatDateTimeDayjs(
          formData.rangeDate[1],
        )}`}</FormItem>
        <FormItem label="活动门槛">店铺会员</FormItem>
        {/* <FormItem label="活动生成人群包">{`${formData.crowdPackage === 1 ? '开启' : '关闭'}`}</FormItem> */}
        <FormItem label="参与门槛">品牌新客</FormItem>
        <FormItem label="出生证明限制">{formData.uploadImg === 1 ? '是' : '否'}</FormItem>
        {formData.uploadImg === 1 && (
          <FormItem label="上传类型设置">{formData.uploadRuleRequestArr.join(',')}</FormItem>
        )}
        <FormItem label="每日客服审批限制">{formData.examineLimit}人</FormItem>
        <FormItem label="活动商品系列数据">
          <Table.StickyLock dataSource={formData.downloadTemplateList} fixedHeader maxBodyHeight={600}>
            <Table.Column lock="left" width={60} title="序号" dataIndex="sortId" />
            <Table.Column width={100} title="系列名" dataIndex="seriesName" />
            <Table.Column width={100} title="正装skuId" dataIndex="formalSku" />
            <Table.Column width={100} title="正装名称" dataIndex="formalName" />
            <Table.Column width={100} title="赠品skuId" dataIndex="giftSku" />
            <Table.Column width={100} title="赠品简称" dataIndex="giftName" />
            <Table.Column width={100} title="赠品编码" dataIndex="giftCode" />
            <Table.Column width={100} title="赠品全称" dataIndex="gitAllName" />
            <Table.Column width={100} title="赠品库存" dataIndex="giftTotal" />
            <Table.Column width={100} title="赠品数量" dataIndex="giftNum" />
            <Table.Column width={100} title="机制" dataIndex="giftRule" />
          </Table.StickyLock>
        </FormItem>

        <FormItem label="活动分享">{formData.shareStatus === 0 ? '关闭' : '开启'}</FormItem>
        {formData.shareStatus !== 0 && (
          <>
            <FormItem label="分享标题">{formData.shareTitle}</FormItem>
            <FormItem label="图文分享图片">
              <img src={formData.h5Img} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="京口令分享图片">
              <img src={formData.cmdImg} style={{ width: '200px' }} alt="" />
            </FormItem>
            <FormItem label="小程序分享图片">
              <img src={formData.mpImg} style={{ width: '200px' }} alt="" />
            </FormItem>
          </>
        )}
        {/* <FormItem label="规则内容"> */}
        {/*  <Input.TextArea className="rule-word-break" value={formData.rules} /> */}
        {/* </FormItem> */}
      </Form>
    </div>
  );
};
