import React, { useReducer, useState } from 'react';
import { SKU_INFO, SkuInfo } from '../../../util';
import { Field, Form, Grid, Input, Loading, Message, NumberPicker } from '@alifd/next';
import styles from './style.module.scss';

interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export const prizeFormLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface SkuDataProps {
  editValue: SkuInfo | null;
  onChange: (data: SkuInfo) => void;
  onCancel: () => void;
  width?: number;
  height?: number;
  formData?: any;
}

export default ({ editValue, onChange, onCancel }: SkuDataProps) => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);

  const [oldSkuData, setOldSkuData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || SKU_INFO);
  console.log(oldSkuData, 'oldSkuData====');
  // 同步/更新数据
  const setData = (data): void => {
    setOldSkuData({
      ...oldSkuData,
      ...data,
    });
  };
  const handleConfirm = (values: any, errors: any) => {
    console.log(oldSkuData, 'oldSkuData====');
    if (errors) {
      return;
    }
    if (!errors) {
      // setData(oldSkuData);
      if (oldSkuData.giftTotal < oldSkuData.giftNum) {
        Message.error('赠品库存不能小于赠品数量');
        return;
      }
      setLoading(true);
      onChange(oldSkuData);
      setLoading(false);
    }
  };
  return (
    <div>
      <Loading visible={loading} inline={false}>
        <Form field={field} {...prizeFormLayout} inline>
          <Form.Item label={'商品系列名称'} required name="seriesName" requiredMessage="请填写商品系列名称">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.seriesName}
              placeholder="请填写商品系列名称"
              maxLength={20}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(seriesName) => {
                setData({ seriesName });
              }}
            />
          </Form.Item>
          <Form.Item label={'正装skuId'} required name="formalSku" requiredMessage="请填写正装skuId">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.formalSku}
              placeholder="请填写正装skuId"
              maxLength={18}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(e: any) => {
                oldSkuData.formalSku = e.replace(/\D/g, '');
                setData(oldSkuData);
              }}
            />
          </Form.Item>
          <Form.Item label={'正装名称'} required name="formalSku" requiredMessage="请填写正装名称">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.formalName}
              placeholder="请填写正装名称"
              maxLength={50}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(formalName) => {
                setData({ formalName });
              }}
            />
          </Form.Item>
          <Form.Item label={'赠品skuId'} required name="giftSku" requiredMessage="请填写赠品skuId">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.giftSku}
              placeholder="请填写赠品skuId"
              maxLength={18}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(e: any) => {
                // setData({giftSku})
                oldSkuData.giftSku = e.replace(/[^0-9.]/g, '');
                setData(oldSkuData);
              }}
            />
          </Form.Item>
          <Form.Item label={'赠品简称'} required name="giftName" requiredMessage="请填写赠品简称">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.giftName}
              placeholder="请填写赠品简称"
              maxLength={20}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(giftName) => {
                setData({ giftName });
              }}
            />
          </Form.Item>
          <Form.Item label={'赠品编码'} required name="giftCode" requiredMessage="请填写赠品编码">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.giftCode}
              placeholder="请填写赠品编码"
              maxLength={18}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(e: any) => {
                // setData({giftCode})
                oldSkuData.giftCode = e.replace(/\D/g, '');
                setData(oldSkuData);
              }}
            />
          </Form.Item>
          <Form.Item label={'赠品全称'} required name="gitAllName" requiredMessage="请填写赠品全称">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.gitAllName}
              placeholder="请填写赠品全称"
              maxLength={50}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(gitAllName) => {
                setData({ gitAllName });
              }}
            />
          </Form.Item>
          <Form.Item label={'赠品库存'} required name="giftTotal" requiredMessage="请填写赠品库存">
            <NumberPicker
              min={1}
              max={99999999}
              placeholder="请填写赠品库存"
              type="inline"
              name={'giftTotal'}
              value={oldSkuData.giftTotal}
              onChange={(giftTotal: any) => {
                setData({ giftTotal });
              }}
              className={styles.number}
            />
          </Form.Item>
          <Form.Item label={'赠品数量'} required name="giftNum" requiredMessage="请填写赠品数量">
            <NumberPicker
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              min={1}
              max={99}
              placeholder="请填写赠品数量"
              type="inline"
              name={'giftNum'}
              value={oldSkuData.giftNum}
              onChange={(giftNum: any) => {
                setData({ giftNum });
              }}
              className={styles.number}
            />
          </Form.Item>
          <Form.Item label={'机制'} name="giftRule" required requiredMessage="请填写机制">
            <Input
              disabled={oldSkuData.operationType === 'edit' || oldSkuData.operationType === 'canEdit'}
              value={oldSkuData.giftRule}
              placeholder="请填写机制"
              maxLength={50}
              showLimitHint
              className={styles.formItemStyle}
              style={{ width: '200px' }}
              onChange={(giftRule) => {
                setData({ giftRule });
              }}
            />
          </Form.Item>
          <Form.Item>
            <Grid.Row>
              <Grid.Col style={{ textAlign: 'right' }}>
                <Form.Submit className="form-btn" validate type="primary" onClick={handleConfirm}>
                  确认
                </Form.Submit>
                <Form.Reset className="form-btn" onClick={onCancel} style={{ marginLeft: '10px' }}>
                  取消
                </Form.Reset>
              </Grid.Col>
            </Grid.Row>
          </Form.Item>
        </Form>
      </Loading>
    </div>
  );
};
