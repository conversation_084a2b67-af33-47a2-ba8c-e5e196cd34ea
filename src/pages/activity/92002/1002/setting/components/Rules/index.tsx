import React, { useEffect, useImperativeHandle, useReducer } from 'react';
import { Button, Field, Form, Input, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import { checkActivityData, FormLayout, generateMembershipString, PageData } from '../../../util';
import format from '@/utils/format';

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  const field: Field = Field.useField();
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useImperativeHandle(sRef, (): { submit: () => object } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  const getRule5 = (v) => {
    if (v.exchangePointType === '1') {
      return `每次兑换单份兑换品需消耗${v.unifyExchangePoint}积分`;
    } else {
      let rule = '';
      v.exchangePoint.forEach((item, index) => {
        rule += `${item.levelName}${item.points}积分${index === v.exchangePoint.length - 1 ? '。' : '，'}`;
      });
      return `按会员等级:${rule}`;
    }
  };
  const getRule6 = (v) => {
    if (v.exchangeLimit === 0) {
      return `活动期内不限制${v.sameTermOnce === true ? '且同期内所有奖品仅限兑换1次 ' : ''}`;
    } else if (v.exchangeLimit === 1) {
      return `活动期限内可兑换${v.exchangeNum}次${v.sameTermOnce === true ? '且同期内所有奖品仅限兑换1次 ' : ''}`;
    } else if (v.exchangeLimit === 2) {
      return `活动期内每日可兑换${v.exchangeNum}次${v.sameTermOnce === true ? '且同期内所有奖品仅限兑换1次 ' : ''}`;
    } else {
      return '';
    }
  };

  const getRule7 = (v) => {
    if (v.levelExchangeTimeLimit === 1) {
      return `
可兑周期：${v.exchangeCycle === 1 ? '活动同时' : `每天${v.exchangeCycleStartTime}-${v.exchangeCycleEndTime}`}`;
    } else {
      let levelExchangeTime = '';
      for (const item of v.levelExchangeTime) {
        if (item.exchangeCycleStartTime === item.exchangeCycleEndTime && item.exchangeCycle === 2) {
          Message.error(`${item.levelName}兑换时间不能相同`);
          return false;
        }
        const timePeriod =
          item.exchangeCycle === 1
            ? '等级兑换时间同步'
            : `每天${item.exchangeCycleStartTime}-${item.exchangeCycleEndTime}`;
        levelExchangeTime += `
${item.levelName}:${item.levelExchangeStartTime}-${item.levelExchangeEndTime} 可兑周期：${timePeriod}，`;
      }
      return `${levelExchangeTime.slice(0, -1)}。`;
    }
  };
  /**
   * 自动生成规则说明
   */
  const autoCreateRuleDesc = async (): Promise<void> => {
    const isValidateData: boolean = checkActivityData(formData);
    if (!isValidateData) {
      return;
    }
    const rulesPop = `1.活动时间：${format.formatDateTimeDayjs(formData.startTime)}至${format.formatDateTimeDayjs(
      formData.endTime,
    )}。
2.活动对象：${generateMembershipString(formData, 'string')}。
3.参与规则：仅活动对象可参与活动。
4.兑换说明：${formData.rightsName}，活动兑换总量共${formData.totalExchangeAmount}份，每日可兑最大份数：${
      formData.exchangeType ? `${formData.prizeCycleTotalNum}份` : '不限'
    }；可兑换数量不代表实际兑换数量，实际兑换数量以兑换成功数量为准。
5.兑换所需积分：${getRule5(formData)}
6.兑换限制：${getRule6(formData)}。
7.会员等级兑换时间：${getRule7(formData)}
8.【活动参与主体资格】
（1）每位自然人用户仅能使用一个京东账号参与活动，WX号、QQ、京东账号、手机号码、收货地址等任一信息一致或指向同一用户的，视为同一个用户，则第一个参与本活动的账号参与结果有效，其余账号参与结果均视为无效。
（2）若发现同一位用户使用不同账号重复参与活动，承办方有权取消其参与资格。
9.【注意事项】
（1）活动过程中，凡以不正当手段（如作弊领取、恶意套取、刷信誉、虚假交易、扰乱系统、实施网络攻击等）参与本次活动的用户，商家有权终止其参与活动，并取消其参与资格（如优惠已发放，商家有权追回），如给商家造成损失的，商家将保留向违规用户继续追索的权利。
（2）如遇不可抗力(包括但不限于重大灾害事件、活动受政府机关指令需要停止举办或调整的、活动中存在大面积作弊行为、活动遭受严重网络攻击或因系统故障导致活动中奖名单大批量出错，活动不能正常进行的)，商家有权取消、修改或暂停本活动。
（3）是否获得优惠以活动发布者后台统计结果为准。
（4）因平台订单接口限流，部分订单可能有所延迟，请您耐心等待，订单状态会持续更新。
（5）法律允许范围内，本活动最终解释权归商家所有。
（6）活动商品数量有限，先到先得。
（7）兑换品兑换成功后，积分不予退回。
`;
    const rules = rulesPop;
    setData({ rules });
    field.setErrors({ rules: '' });
  };
  return (
    <div>
      <LzPanel title="活动规则">
        <Form {...formItemLayout} field={field}>
          <FormItem label="规则内容" required requiredMessage="请生成/输入规则内容">
            <Input.TextArea
              value={formData.rules}
              name="rules"
              onChange={(rules) => setData({ rules })}
              autoHeight={{ minRows: 8, maxRows: 40 }}
              placeholder="请输入活动规则说明"
              maxLength={5000}
              showLimitHint
              className="form-input-ctrl"
            />
          </FormItem>
          <FormItem label=" " colon={false}>
            <Button
              type="primary"
              className="table-cell-btn"
              onClick={autoCreateRuleDesc}
              style={{ marginRight: '15px' }}
              text
            >
              自动生成规则说明
            </Button>
            <span style={{ color: 'red', fontSize: '12px' }}>
              提示：点击按钮自动生成规则，也可以自己编辑信息，手机端规则处显示。
            </span>
          </FormItem>
        </Form>
      </LzPanel>
    </div>
  );
};
