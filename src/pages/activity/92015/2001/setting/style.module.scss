.setting {
  margin-top: 15px;
}

.panel {
  box-sizing: border-box;
  background: #f5f7f9;
  padding: 15px;
  border: 1px solid #d7dde4;
  min-width: 350px;
}
.number {
  margin: 0 10px;
}
.tip {
  font-size: 12px;
  color: gray;
  margin-top: 15px;
}
.tips {
  font-size: 12px;
}
.container {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 10px;
  margin-top: 15px;

  .skuContainer {
    display: flex;
    justify-content: center;
    align-items: center;
    padding: 10px;
    border: 1px solid lightgray;
    position: relative;

    &:hover {
      .del {
        display: block;
      }
    }

    .skuImg {
      width: 60px;
      height: 60px;
    }

    .skuName {
      max-width: 120px;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .skuId {
      color: lightgray;
      font-size: 12px;
    }

    .price {
      color: red;
      font-weight: bold;
      margin-bottom: 5px;
    }
  }
}
