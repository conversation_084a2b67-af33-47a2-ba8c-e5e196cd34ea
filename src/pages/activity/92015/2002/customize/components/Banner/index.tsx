/**
 * Author: linrong<PERSON>
 * Date: 2025-06-25 18:02
 * Description: banner
 */
import React, {useReducer, useEffect} from 'react';
import styles from './style.module.scss';
import {Form, Button, Grid, Switch, Input} from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import {CustomValue, FormLayout} from '../../../util';
import LzPanel from '@/components/LzPanel';
import {activityEditDisabled, deepCopy} from "@/utils";

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 5,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({defaultValue, value, onChange}: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return {...p, ...c};
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({...formData, ...obj});
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);

  return (
    <div className={styles.base}>
      <LzPanel title="轮播设置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item required label="是否开启轮播">
            <Switch
              checked={formData.isShowBanner}
              autoWidth
              checkedChildren={'已启用'}
              unCheckedChildren={'已关闭'}
              onChange={(isShowBanner) => {
                setForm({isShowBanner});
              }}
            />
          </Form.Item>
          {formData.isShowBanner &&
            <LzPanel>
              {formData.bannerList.map((item: any, index: number) =>
                <>
                  {!activityEditDisabled() && (
                    <div style={{display: 'flex', justifyContent: 'flex-end'}}>
                      <Button
                        text
                        type="primary"
                        onClick={() => {
                          const bannerList = deepCopy(formData.bannerList);
                          bannerList.splice(index, 1);
                          setForm({bannerList});
                        }}
                      >
                        <i className={`iconfont icon-shanchu`}/>
                      </Button>
                    </div>
                  )}
                  <Form.Item label="轮播图" required>
                    <Grid.Row>
                      <Form.Item style={{marginRight: 10, marginBottom: 0}}>
                        <LzImageSelector
                          width={714}
                          value={item.bannerImage}
                          onChange={(bannerImage) => {
                            const bannerList = deepCopy(formData.bannerList);
                            bannerList[index].bannerImage = bannerImage;
                            setForm({bannerList});
                          }}
                        />
                      </Form.Item>
                      <Form.Item style={{marginBottom: 0}}>
                        <div className={styles.tip}>
                          <p>图片尺寸：宽度714px、推荐图片高度280px</p>
                          <p>图片大小：不超过1M</p>
                          <p>图片格式：JPG、JPEG、PNG、GIF</p>
                        </div>
                        <div>
                          <Button
                            type="primary"
                            text
                            onClick={() => {
                              const bannerList = deepCopy(formData.bannerList);
                              bannerList[index].bannerImage = formData.bannerExampleImage;
                              setForm({bannerList});
                            }}
                          >
                            重置
                          </Button>
                        </div>
                      </Form.Item>
                    </Grid.Row>
                  </Form.Item>
                  <Form.Item required label="是否开启跳转">
                    <Switch
                      checked={item.isShowJump}
                      autoWidth
                      checkedChildren={'已启用'}
                      unCheckedChildren={'已关闭'}
                      onChange={(isShowJump) => {
                        const bannerList = deepCopy(formData.bannerList);
                        bannerList[index].isShowJump = isShowJump;
                        setForm({bannerList});
                      }}
                    />
                  </Form.Item>
                  {item.isShowJump && (
                    <Form.Item required label="跳转链接">
                      <Grid.Row>
                        <Input
                          value={item.bannerLink}
                          onChange={(bannerLink) => {
                            const bannerList = deepCopy(formData.bannerList);
                            bannerList[index].bannerLink = bannerLink;
                            setForm({bannerList});
                          }}
                          className="w-300"
                          placeholder="请输入跳转链接"
                        />
                      </Grid.Row>
                    </Form.Item>
                  )}
                </>
              )}

              <Button
                disabled={formData.bannerList?.length >= 5}
                type="secondary"
                style={{width: '100%'}}
                onClick={() => {
                  const bannerList = deepCopy(formData.bannerList);
                  bannerList.push({
                    bannerImage: formData.bannerExampleImage,
                    isShowJump: true,
                    bannerLink: '',
                  });
                  setForm({bannerList});
                }}
              >
                {formData.bannerList?.length}/5添加轮播
              </Button>
            </LzPanel>
          }
        </Form>
      </LzPanel>
    </div>
  );
};
