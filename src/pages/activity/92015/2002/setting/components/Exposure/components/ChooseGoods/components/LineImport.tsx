import React, { useState } from 'react';
import LzPagination, { Pager } from '@/components/LzPagination';
import { <PERSON>oon, Button, Divider, Message, Table } from '@alifd/next';
import { getSkuGroup, importSkuText } from '@/api/sku';

const initPager: Pager = {
  pageNum: 1,
  pageSize: 20,
  total: 0,
};

export default ({ max = 0, uploadList, cancel, confirm }) => {
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<any[]>([]);
  const [selectedRowKeys, setSelectedRowKeys] = useState<string>('');
  const [selectedKeys, setSelectedKeys] = useState<string[]>([]);
  const [successLists, setSuccessLists] = useState<any[]>([]);

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]): void => {
    setSelectedKeys(selectKey);
    const result = list
      .filter((item) => selectKey.includes(item.id))
      .map((e) => e.skus)
      .join(',');
    setSelectedRowKeys(result);
  };

  const rowSelection: {
    mode: 'single' | 'multiple' | undefined;
    selectedRowKeys: string[];
    onChange: (selectKey: string[]) => void;
  } = {
    mode: 'multiple',
    selectedRowKeys: selectedKeys,
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
  };
  const fetchData = async (pageData = {}) => {
    setLoading(true);
    const params: any = { ...initPager, ...pageData };
    const data = await getSkuGroup(params);
    setLoading(false);
    setList(data.records!);
    setPageInfo({
      pageNum: data.current,
      pageSize: data.size,
      total: data.total,
    } as any);
  };

  const saveSkuIds = async () => {
    setLoading(true);
    try {
      const { successList } = await importSkuText({ skuIdCount: selectedRowKeys.split(',') });
      setSuccessLists(successList || []);
      setLoading(false);
      uploadList(successList);
      rowSelection.onChange([]);
    } catch (e) {
      Message.error(e.message);
      setLoading(false);
    }
  };

  React.useEffect(() => {
    fetchData().then();
  }, []);

  return (
    <div>
      <Table
        loading={loading}
        primaryKey="id"
        fixedHeader
        maxBodyHeight={270}
        dataSource={list}
        rowSelection={rowSelection}
      >
        <Table.Column title={'产品线名称'} dataIndex={'productLineName'} />
        <Table.Column
          title={'SKU列表'}
          width={200}
          cell={(val, index, _) => (
            <div>
              <span>{_.skuNumber}个</span>
              <Balloon
                v2
                align="t"
                trigger={
                  <Button type={'primary'} text>
                    详细信息
                  </Button>
                }
                closable={false}
              >
                <div style={{ maxHeight: 400, overflowY: 'scroll' }}>{_.skus}</div>
              </Balloon>
            </div>
          )}
        />
      </Table>
      <LzPagination
        total={pageInfo.total}
        pageNum={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        onChange={fetchData}
      />
      <div>
        <Button type={'primary'} onClick={saveSkuIds}>
          确定导入
        </Button>
      </div>
      <Divider />
      <div style={{ textAlign: 'right' }}>
        <Button
          type={'primary'}
          onClick={() => confirm(successLists)}
          style={{ marginRight: 8 }}
          disabled={successLists.length === 0 || max < successLists.length}
        >
          确定
        </Button>
        <Button type={'secondary'} onClick={cancel}>
          取消
        </Button>
      </div>
    </div>
  );
};
