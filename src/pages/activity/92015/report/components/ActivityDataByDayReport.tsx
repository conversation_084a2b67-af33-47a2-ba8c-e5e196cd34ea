import React, { useEffect, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Message, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { userActRecordPage, userActRecordPageExport } from '@/api/v92015Data';
import Utils, { deepCopy, downloadExcel, getParams } from '@/utils';
import dayJs from 'dayjs';
import dayjs from 'dayjs';

const { RangePicker } = DatePicker2;
const FormItem = Form.Item;
const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [packVisible, setPackVisible] = useState(false);
  const [timeRange, setTimeRange] = useState([
    dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
    dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
  ]);
  const handleSubmit = (e) => {
    e.preventDefault();
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  };

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    query.dateRange = timeRange;
    userActRecordPage(query)
      .then((res: any): void => {
        setTableData(res as any[]);
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const handlePage = ({ pageSize, pageNum }) => {
    setPage({
      ...pageInfo,
      pageSize,
      pageNum,
    });
    const formValue: any = field.getValues();
    loadData({ ...formValue, pageSize, pageNum });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    userActRecordPageExport(formValue).then((data: any) => downloadExcel(data, '单活动累计数据报表'));
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue, ...defaultPage });
  }, []);

  return (
    <div>
      <Form className="lz-query-criteria" field={field} colon labelAlign={'top'} onSubmit={handleSubmit}>
        <FormItem name="dateRange" label="活动时间">
          <RangePicker
            hasClear={false}
            defaultValue={timeRange}
            showTime
            value={timeRange}
            onChange={(val: any) => setTimeRange([val[0].format('YYYY-MM-DD'), val[1].format('YYYY-MM-DD')])}
            format="YYYY-MM-DD"
          />
        </FormItem>
        <FormItem colon={false}>
          <Form.Submit type="primary" htmlType="submit">
            查询
          </Form.Submit>
          <Form.Reset
            toDefault
            onClick={() => {
              const formValue: any = field.getValues();
              setTimeRange([
                dayJs(new Date(getParams('startTime'))).format('YYYY-MM-DD'),
                dayJs(new Date(getParams('endTime'))).format('YYYY-MM-DD'),
              ]);
              loadData({ ...formValue, ...defaultPage });
            }}
          >
            重置
          </Form.Reset>
        </FormItem>
      </Form>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={tableData} loading={loading}>
        <Table.Column title="日期" width={250} dataIndex="activityTime" />
        <Table.Column title="PV" width={150} dataIndex="pv" />
        <Table.Column title="UV" width={150} dataIndex="uv" />
        <Table.Column title="入会人数" width={200} dataIndex="openCardUser" />
        <Table.Column title="兑换人数" width={200} dataIndex="receiveUser" />
        <Table.Column title="兑换件数" width={200} dataIndex="receiveNum" />
        <Table.Column title="下单人数" width={200} dataIndex="buyUserNum" />
        <Table.Column title="下单金额" width={200} dataIndex="buyNum" />
        <Table.Column title="下单商品件数" width={200} dataIndex="buySkuNum" />
        {tableData[0]?.couponList?.map((item: any, index: number) => (
          <Table.ColumnGroup title={`优惠券${item.prizeKey}`}>
            <Table.Column
              title={`优惠券${item.prizeKey}下单人数`}
              dataIndex="couponList"
              width={200}
              key={index}
              cell={(value, idx, record) => <span>{record.couponList[index].couponUser}</span>}
            />
            <Table.Column
              title={`优惠券${item.prizeKey}下单商品件数`}
              dataIndex="couponList"
              width={200}
              key={index}
              cell={(value, idx, record) => <span>{record.couponList[index].couponSku}</span>}
            />
            <Table.Column
              title={`优惠券${item.prizeKey}下单金额`}
              dataIndex="couponList"
              width={200}
              key={index}
              cell={(value, idx, record) => <span>{record.couponList[index].couponPrice}</span>}
            />
          </Table.ColumnGroup>
        ))}
      </Table>
    </div>
  );
};
