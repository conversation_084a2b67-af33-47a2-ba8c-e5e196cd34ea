import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { activityEditDisabled, getParams } from '@/utils';
import { getShop } from '@/utils/shopUtil';
import { FormLayout } from '@/types/Form';

class Sku {
  jdPrice: string;
  seq: number;
  skuId: number;
  skuMainPicture: string;
  skuName: string;
}

export interface CustomValue {
  // 页面背景图
  pageBg: string;
  // 活动攻略图
  actStrategy: string;
  // 报名成功弹窗图
  signSuccessPopup: string;
  // 报名失败弹窗图
  signFailPopup: string;
  // 报名失败跳转链接(符合条件订单=0)
  signFailBtnUrl0: string;
  // 报名失败跳转链接(符合条件订单>1)
  signFailBtnUrl1: string;
  // 抽奖失败跳转链接
  drawFailBtnUrl: string;
  // 抽奖失败弹窗图
  drawFailPopup: string;
  // 京口令分享图片
  cmdImg: string;
  // 图文分享图片
  h5Img: string;
  // 小程序分享图片
  mpImg: string;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
  type: number;
  peopleNum: number;
  rank: string;
}

export interface PageData {
  // 活动名称
  activityName: string;
  // 店铺名称
  shopName: string;
  // 日期区间（不提交）
  rangeDate: string[];
  // 活动开始时间
  startTime: string;
  // 活动结束时间
  endTime: string;
  // 活动门槛
  threshold: number;
  // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
  supportLevels: string;
  // 限制入会时间 0：不限制；1：入会时间
  limitJoinTimeType: number;
  // 入会时间
  joinTimeRange: any[];
  joinStartTime: string;
  joinEndTime: string;
  // 参与活动规则的门槛 1无门槛 2会员 3非会员
  identityLimitType: number;
  // 报名相关
  signStartTime: string;
  signEndTime: string;
  signStartEnd: string;
  signDays: number;
  signOrderNumType: number;
  signMinPrice: string;
  signMaxPrice: string;
  signSkuType: number;
  signSkuList: any[];
  // 复购相关
  moreStartTime: string;
  moreEndTime: string;
  moreMinPrice: string;
  moreMaxPrice: string;
  moreDays: number;
  moreSkuType: number;
  moreSkuList: any[];
  // 曝光商品列表
  exposureSkuList: any[];
  // 曝光商品列表预览
  exposureSkuListPreview: any[];
  // 奖品列表
  prizeList: any[];
  // 分享相关
  shareStatus: number;
  shareTitle: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  // 其他配置
  rules: string;
  templateCode: string;
  gradeLabel: any[];
  crowdBag: any;
  // 限制类型
  orderLimitType: number;
  judgeLimitType: number;
  amtLimit: string;
}

interface PrizeType {
  [key: number]: string;
}

export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  pageBg: '',
  actStrategy: '',
  signSuccessPopup: '',
  signFailPopup: '',
  drawFailPopup: '',
  signFailBtnUrl0: '',
  signFailBtnUrl1: '',
  drawFailBtnUrl: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
};

// 活动设置默认数据
export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  type: 0, // 0门槛奖品 1排行榜奖品
  peopleNum: 1,
  rank: '', // 获奖名次 默认0
};

// 活动设置默认数据
export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `复购礼-${dayjs().format('YYYY-MM-DD')}`,
    // 店铺名称
    shopName: getShop().shopName,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    identityLimitType: 2, // 参与活动规则的门槛  1无门槛 2会员 3非会员

    signStartTime: '', // 报名的开始时间
    signEndTime: '', // 报名的结束时间
    signStartEnd: '', // 报名的结束时间
    signDays: 365, // 报名的天数
    signOrderNumType: 1, // 报名订单数量0等于1笔1大于等于1笔
    signMinPrice: '', // 报名的最小金额
    signMaxPrice: '', // 报名的最大金额
    signSkuType: 1, // 报名的商品类型 1 全部商品 2 指定商品
    signSkuList: [], // 报名的商品列表

    moreStartTime: '', // 复购订单的开始时间
    moreEndTime: '', // 复购订单的结束时间
    moreMinPrice: '', // 复购订单的最小金额
    moreMaxPrice: '', // 复购订单的最大金额
    moreDays: 7, // 复购订单完成+N天
    moreSkuType: 1, // 复购订单的商品类型 1 全部商品 2 指定商品
    moreSkuList: [], // 复购订单的商品列表

    exposureSkuList: [], // 曝光商品列表
    exposureSkuListPreview: [], // 曝光商品列表预览

    // 奖品列表
    prizeList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '用户下单即有机会获得好礼',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,

    orderLimitType: 2, // 1新客 2老客
    judgeLimitType: 2, // 1 历史 2 订单 3 不限制
    amtLimit: '',
  };
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.identityLimitType === 2) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    // const joinTimeLimitString =
    //   formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return membershipString + (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`);
  }
  if (formData.identityLimitType === 3) {
    return '非会员';
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};

const checkPrizeSetting = (formData: PageData) => {
  if (formData.prizeList?.length === 0) {
    Message.error('请添加奖品');
    return false;
  }
  for (let i = 0; i < formData.prizeList.length; i++) {
    if (!formData.prizeList[i].prizeName) {
      Message.error('请选择奖品');
      return false;
    }
  }
  return true;
};
const checkShare = (formData: PageData) => {
  if (formData.shareStatus === 1) {
    if (!formData.shareTitle) {
      Message.error('请输入分享标题');
      return false;
    }
    if (!formData.h5Img) {
      Message.error('请设置图文分享图片');
      return false;
    }
    if (!formData.cmdImg) {
      Message.error('请设置京口令分享图片');
      return false;
    }
    if (!formData.mpImg) {
      Message.error('请设置小程序分享图片');
      return false;
    }
  }
  return true;
};
export const checkActivityData = (formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (!activityEditDisabled()) {
    // 开始时间异常
    if (!isStartTimeValid(formData.startTime)) {
      return false;
    }
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  if (
    dayjs(formData.signStartTime).isBefore(formData.startTime) ||
    dayjs(formData.signEndTime).isAfter(formData.endTime)
  ) {
    Message.error('报名时间需在活动时间内');
    return false;
  }
  if (formData.signMinPrice > formData.signMaxPrice) {
    Message.error('报名订单金额范围最小值需小于最大值');
    return false;
  }
  if (dayjs(formData.moreEndTime).isAfter(formData.endTime)) {
    Message.error('复购订单结束时间需在活动结束时间前');
    return false;
  }
  if (dayjs(formData.moreEndTime).isBefore(formData.signEndTime)) {
    Message.error('复购订单结束时间需在报名结束时间后');
    return false;
  }
  if (formData.moreMinPrice > formData.moreMaxPrice) {
    Message.error('复购订单金额范围最小值需小于最大值');
    return false;
  }
  // 奖品设置部分校验
  if (!checkPrizeSetting(formData)) {
    return false;
  }

  if (!checkShare(formData)) {
    return false;
  }
  return true;
};
