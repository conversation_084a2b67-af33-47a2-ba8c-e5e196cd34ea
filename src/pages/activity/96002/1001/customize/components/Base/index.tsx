import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import {Form, Button, Grid, Input} from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import { CustomValue, FormLayout } from '../../../util';
import LzPanel from '@/components/LzPanel';
import LzColorPicker from '@/components/LzColorPicker';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 7,
  },
  colon: true,
};

interface Props {
  defaultValue: Partial<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}

export default ({ defaultValue, value, onChange }: Props) => {
  // 装修数据 || 初始数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  // 更新数据，向上传递
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.base}>
      <LzPanel
        actions={
          <Button
            type="primary"
            text
            onClick={() => {
              window.location.href =
                'https://bigitem.oss-cn-zhangjiakou.aliyuncs.com/crm/sass/psd/%E5%8D%A1%E5%88%AB%E7%A4%BC.psb';
            }}
          >
            下载psd素材包
          </Button>
        }
      >
        <Form {...formLayout} className={styles.form}>
          {/*<Form.Item label="活动主页图">*/}
          {/*  <Grid.Row>*/}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>*/}
          {/*      <LzImageSelector*/}
          {/*        width={750}*/}
          {/*        value={formData.actBg}*/}
          {/*        onChange={(actBg) => {*/}
          {/*          setForm({ actBg });*/}
          {/*        }}*/}
          {/*      />*/}
          {/*    </Form.Item>*/}
          {/*    <Form.Item style={{ marginBottom: 0 }}>*/}
          {/*      <div className={styles.tip}>*/}
          {/*        <p>图片尺寸：宽度750px、推荐图片高度1630px</p>*/}
          {/*        <p>图片大小：不超过1M</p>*/}
          {/*        <p>图片格式：JPG、JPEG、PNG、GIF</p>*/}
          {/*      </div>*/}
          {/*      <div>*/}
          {/*        <Button*/}
          {/*          type="primary"*/}
          {/*          text*/}
          {/*          onClick={() => {*/}
          {/*            setForm({ actBg: defaultValue?.actBg });*/}
          {/*          }}*/}
          {/*        >*/}
          {/*          重置*/}
          {/*        </Button>*/}
          {/*      </div>*/}
          {/*    </Form.Item>*/}
          {/*  </Grid.Row>*/}
          {/*</Form.Item>*/}
           <Form.Item label="页面背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  value={formData.pageBg}
                  onChange={(pageBg) => {
                    setForm({ pageBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：宽度750px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ pageBg: defaultValue?.pageBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
           </Form.Item>
          <Form.Item label="页面背景颜色">
            <LzColorPicker value={formData.actBgColor} onChange={(actBgColor) => setForm({ actBgColor })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ actBgColor: defaultValue?.actBgColor });
              }}
            >
              重置
            </Button>
          </Form.Item>
          <Form.Item label="即刻开启按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={370}
                  height={94}
                  value={formData.getBtnBg}
                  onChange={(getBtnBg) => {
                    setForm({ getBtnBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：370px*94px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG、GIF</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ getBtnBg: defaultValue?.getBtnBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="未填过信息跳转链接" required>
            <Input
              value={formData.finishInfoLink}
              onChange={(finishInfoLink) => setForm({ finishInfoLink })}
              className={styles.popupLink}
              placeholder="请输入跳转链接"
            />
            <div className={styles.tips}>此链接为完善信息活动链接，请先创建完善信息活动，再将链接填入此处</div>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
