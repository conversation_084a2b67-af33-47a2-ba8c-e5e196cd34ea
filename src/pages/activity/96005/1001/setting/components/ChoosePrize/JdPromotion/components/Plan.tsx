import * as React from 'react';
import { useEffect, useState } from 'react';
import { Dialog, Pagination, Table } from '@alifd/next';
import styles from '../index.module.scss';
import SearchForm from './SearchForm';
import { getPromotionSoPageList } from '@/api/prize';
import format from '@/utils/format';
// import CreatePlan from './CreatePlan';
import ImportPlan from '@/components/ChoosePrize/JdCoupon/components/ImportPlan';

const defaultPage = { pageSize: 10, pageNum: 1, total: 0 };
export default ({ onSubmit, promoType = '', planList = [] }) => {
  const [pageInfo, setPageInfo] = useState({ ...defaultPage });
  const [searchData, setSearchData] = useState({});
  const [datalist, setList] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const [planDialog, setPlanDialog] = useState(false);
  const [importPlanDialog, setImportPlanDialog] = useState(false);

  // 加载列表
  const loadPageList = (page) => {
    setLoading(true);
    getPromotionSoPageList({ promoType, ...searchData, ...page })
      .then((res) => {
        setList(res.records || []);
        pageInfo.total = res.total as any;
        pageInfo.pageNum = res.current as any;
        setPageInfo(pageInfo);
        setLoading(false);
      })
      .catch(() => {
        setLoading(false);
      });
  };

  // 分页事件
  const pageChangeHandler = (pageNum: number) => {
    pageInfo.pageNum = pageNum;
    loadPageList(pageInfo);
  };
  const handlePageSizeChange = (pageSize: number) => {
    pageInfo.pageSize = pageSize;
    pageInfo.pageNum = 1;
    loadPageList(pageInfo);
  };

  // 当选中行的时候
  const onRowSelectionChange = (selectKey: string[]) => {
    const selectRow = datalist.find((o: any) => selectKey.some((p: any) => p === o.secretKey));
    onSubmit(selectRow);
  };

  // 选择器
  const rowSelection: any = {
    mode: 'single',
    onChange: (selectKey: string[]) => onRowSelectionChange(selectKey),
    getProps: (record: { secretKey: string }) => {
      return {
        disabled: false,
      };
    },
  };

  // 当行点击的时候
  const onRowClick = (record: object) => {
    // 预留，如果要求点击行就选择的话，就解开这行
    // onSubmit(record);
  };
  const onSearch = (formData: any) => {
    setSearchData(formData);
    loadPageList({ ...formData, ...defaultPage });
  };
  const parsePointResListStatus = (status: number) => {
    switch (status) {
      case 1:
        return '待生效';
      case 2:
        return '已生效';
      case 3:
        return '已过期';
      default:
        return '已过期';
    }
  };
  const parsePointResListStatusColor = (status: number) => {
    switch (status) {
      case 1:
        return 'table-status-WARNING_COLOR';
      case 2:
        return 'table-status-SUCCESS_COLOR';
      default:
        return 'table-status-LIGHT_GRAY';
    }
  };

  useEffect(() => {
    loadPageList(defaultPage);
  }, []);

  return (
    <div className={styles.PropertyJdBeanPlan}>
      <SearchForm onSearch={onSearch} />
      {/* <div className={styles.reminderBox}> */}
      {/*   <span className={styles.totalStyle}>共有 {pageInfo.total} 条符合条件的记录</span> */}
      {/*   {!venderType && ( */}
      {/*     <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={toCouponList}> */}
      {/*       新建优惠券 &gt; */}
      {/*     </Button> */}
      {/*   )} */}
      {/*   {!!venderType && ( */}
      {/*     <Button className="table-cell-btn" style={{ marginLeft: '0' }} type="primary" text onClick={importCouponList}> */}
      {/*       导入优惠券 &gt; */}
      {/*     </Button> */}
      {/*   )} */}
      {/* </div> */}
      {!promoType && <div>注：已过滤过期、关联活动</div>}
      {promoType === '1' && <div>注：已过滤过期、关联活动、总价促销的令牌</div>}
      {promoType === '10' && <div>注：已过滤过期、关联活动、单品促销的令牌</div>}
      <Table.StickyLock
        dataSource={datalist}
        primaryKey="secretKey"
        fixedHeader
        maxBodyHeight={300}
        loading={loading}
        onRowClick={onRowClick}
        rowSelection={rowSelection}
      >
        <Table.Column
          width={120}
          align="left"
          title="令牌信息"
          cell={(value, index, data) => (
            <div>
              <div>{data.name}</div>
              <div>令牌ID：{data.tokenId}</div>
              <div>令牌密钥：{data.secretKey}</div>
            </div>
          )}
        />
        <Table.Column
          width={180}
          align="left"
          title="令牌有效时间"
          cell={(value, index, data) => (
            <div>
              <div>起：{format.formatDateTimeDayjs(new Date(data.beginTime))}</div>
              <div>止：{format.formatDateTimeDayjs(new Date(data.endTime))}</div>
            </div>
          )}
        />
        <Table.Column
          width={80}
          align="left"
          title="促销活动类型"
          cell={(value, index, data) => <div>{data.promoType === 1 ? '单品促销' : '总价促销'}</div>}
        />
        <Table.Column
          width={80}
          align="left"
          title="时效状态"
          cell={(value, index, data) => (
            <span className={styles[parsePointResListStatusColor(data.planStatus)]}>
              {parsePointResListStatus(data.planStatus)}
            </span>
          )}
        />
      </Table.StickyLock>
      <Pagination
        shape="arrow-only"
        pageSizeSelector="dropdown"
        pageSizePosition="end"
        total={pageInfo.total}
        current={pageInfo.pageNum}
        pageSize={pageInfo.pageSize}
        pageSizeList={[5, 10, 30, 50, 100]}
        totalRender={(total) => `共${total}条`}
        onPageSizeChange={handlePageSizeChange}
        onChange={pageChangeHandler}
        className={styles.pagination}
      />
      <Dialog
        title="新建优惠券"
        footer={false}
        shouldUpdatePosition
        visible={planDialog}
        onClose={() => setPlanDialog(false)}
      >
        {/* <CreatePlan handleCancel={() => setPlanDialog(false)} handleSubmit={createPlanSubmit} /> */}
      </Dialog>
      <Dialog
        title="导入优惠券"
        footer={false}
        shouldUpdatePosition
        visible={importPlanDialog}
        onClose={() => setImportPlanDialog(false)}
      >
        <ImportPlan
          close={(val) => {
            if (val) {
              loadPageList(defaultPage);
              setImportPlanDialog(false);
            } else {
              setImportPlanDialog(false);
            }
          }}
        />
      </Dialog>
    </div>
  );
};
