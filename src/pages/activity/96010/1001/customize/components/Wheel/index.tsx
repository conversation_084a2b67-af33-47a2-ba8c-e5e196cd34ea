import React, { useReducer, useEffect } from 'react';
import styles from './style.module.scss';
import { Form, Button, Grid } from '@alifd/next';
import LzImageSelector from '@/components/LzImageSelector';
import LzColorPicker from '@/components/LzColorPicker';
import LzPanel from '@/components/LzPanel';
import { CustomValue, FormLayout } from '../../../util';

const formLayout: Omit<FormLayout, 'wrapperCol' | 'labelAlign'> = {
  labelCol: {
    fixedSpan: 6,
  },
  colon: true,
};
interface Props {
  defaultValue: Required<CustomValue>;
  value: CustomValue | undefined;
  onChange: (formData: Required<CustomValue>) => void;
}
export default ({ defaultValue, value, onChange }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const setForm = (obj): void => {
    setFormData(obj);
    onChange({ ...formData, ...obj });
  };
  // 用于处理装修组件重新挂载赋值问题
  // 重置接口返回默认值
  useEffect(() => {
    setFormData(value || defaultValue);
  }, [value, defaultValue]);
  return (
    <div className={styles.wheel}>
      <LzPanel title="盲盒设置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="盲盒背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  height={1038}
                  value={formData.blindBoxBg}
                  onChange={(blindBoxBg) => {
                    setForm({ blindBoxBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：750px * 1038px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ blindBoxBg: defaultValue?.blindBoxBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          {/* <Form.Item label="盲盒图"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={125} */}
          {/*        height={160} */}
          {/*        value={formData.blindBox1} */}
          {/*        onChange={(blindBox1) => { */}
          {/*          setForm({ blindBox1 }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：125px * 160px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：JPG、JPEG、PNG</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ blindBox1: defaultValue?.blindBox1 }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
          {/* <Form.Item label="选中盲盒图"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={133} */}
          {/*        height={168} */}
          {/*        value={formData.blindBox1Light} */}
          {/*        onChange={(blindBox1Light) => { */}
          {/*          setForm({ blindBox1Light }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：133px * 168px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：JPG、JPEG、PNG</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ blindBox1Light: defaultValue?.blindBox1Light }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
          {/* <Form.Item label="盲盒动效图"> */}
          {/*  <Grid.Row> */}
          {/*    <Form.Item style={{ marginRight: 10, marginBottom: 0 }}> */}
          {/*      <LzImageSelector */}
          {/*        width={570} */}
          {/*        height={595} */}
          {/*        value={formData.blindGif} */}
          {/*        onChange={(blindGif) => { */}
          {/*          setForm({ blindGif }); */}
          {/*        }} */}
          {/*      /> */}
          {/*    </Form.Item> */}
          {/*    <Form.Item style={{ marginBottom: 0 }}> */}
          {/*      <div className={styles.tip}> */}
          {/*        <p>图片尺寸：570px * 595px</p> */}
          {/*        <p>图片大小：不超过1M</p> */}
          {/*        <p>图片格式：GIF</p> */}
          {/*      </div> */}
          {/*      <div> */}
          {/*        <Button */}
          {/*          type="primary" */}
          {/*          text */}
          {/*          onClick={() => { */}
          {/*            setForm({ blindGif: defaultValue?.blindGif }); */}
          {/*          }} */}
          {/*        > */}
          {/*          重置 */}
          {/*        </Button> */}
          {/*      </div> */}
          {/*    </Form.Item> */}
          {/*  </Grid.Row> */}
          {/* </Form.Item> */}
          <Form.Item label="选我按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={101}
                  height={36}
                  value={formData.drawBtn}
                  onChange={(drawBtn) => {
                    setForm({ drawBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：101px * 36px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ drawBtn: defaultValue?.drawBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="选我置灰按钮">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={101}
                  height={36}
                  value={formData.grayDrawBtn}
                  onChange={(grayDrawBtn) => {
                    setForm({ grayDrawBtn });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：101px * 36px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ grayDrawBtn: defaultValue?.grayDrawBtn });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzPanel title="奖品剩余份数">
        {' '}
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="文案颜色">
            <LzColorPicker value={formData.prizeRemainNum} onChange={(prizeRemainNum) => setForm({ prizeRemainNum })} />
            <Button
              type="primary"
              text
              onClick={() => {
                setForm({ prizeRemainNum: defaultValue?.prizeRemainNum });
              }}
            >
              重置
            </Button>
          </Form.Item>
        </Form>
      </LzPanel>
      <LzPanel title="获奖名单设置">
        <Form {...formLayout} className={styles.form}>
          <Form.Item label="获奖名单背景图">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={750}
                  height={326}
                  value={formData.winnerBg}
                  onChange={(winnerBg) => {
                    setForm({ winnerBg });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：750px * 326px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ winnerBg: defaultValue?.winnerBg });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
          <Form.Item label="获奖名单弹幕背景">
            <Grid.Row>
              <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
                <LzImageSelector
                  width={455}
                  height={83}
                  value={formData.winnersDanMu}
                  onChange={(winnersDanMu) => {
                    setForm({ winnersDanMu });
                  }}
                />
              </Form.Item>
              <Form.Item style={{ marginBottom: 0 }}>
                <div className={styles.tip}>
                  <p>图片尺寸：455px * 83px</p>
                  <p>图片大小：不超过1M</p>
                  <p>图片格式：JPG、JPEG、PNG</p>
                </div>
                <div>
                  <Button
                    type="primary"
                    text
                    onClick={() => {
                      setForm({ winnersDanMu: defaultValue?.winnersDanMu });
                    }}
                  >
                    重置
                  </Button>
                </div>
              </Form.Item>
            </Grid.Row>
          </Form.Item>
        </Form>
      </LzPanel>
    </div>
  );
};
