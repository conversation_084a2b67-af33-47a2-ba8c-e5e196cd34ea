import React from 'react';
import Pop from './Pop';
import { isPopShop } from '@/utils';

/**
 * 组件props注释
 *
 * @param {Function} submit - 提交事件处理函数
 * @param {any} value - 组件值
 * @param {boolean} disabled - 是否禁用组件
 * @param {boolean} onlyOne - 是否只允许选择/创建1个折扣商品,默认为false
 */
export default ({ submit, value, disabled, onlyOne = false }) => {
  const eventProps: any = {
    value,
    disabled,
    submit,
    onlyOne,
  };
  return <div>{isPopShop() && <Pop {...eventProps} />}</div>;
};
