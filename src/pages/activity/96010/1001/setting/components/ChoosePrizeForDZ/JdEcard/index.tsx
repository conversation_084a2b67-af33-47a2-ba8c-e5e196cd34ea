import {
  But<PERSON>,
  DatePicker2,
  Dialog,
  Field,
  Form,
  Grid,
  Input,
  Message,
  NumberPicker,
  Radio,
  Switch,
} from '@alifd/next';
import React, { useReducer, useState } from 'react';
import Plan from './components/Plan';
import active from '../assets/active.png';
import notActive from '../assets/not-active.png';
import delIcon from '../assets/del-icon.png';
import styles from './index.module.scss';
import LzImageSelector from '@/components/LzImageSelector';
import { activityEditDisabled, numRegularCheckInt } from '@/utils';
import format from '@/utils/format';
import { prizeFormLayout } from '@/components/ChoosePrize';
import constant from '@/utils/constant';

interface ComponentProps {
  [propName: string]: any;
}

const PropertyEcard = ({
  editValue,
  onChange,
  onCancel,
  hasProbability = true,
  hasLimit = true,
  hasShowTime = false,
  width,
  height,
  prizeNameLength,
  formData,
  sendTotalCountMax = 999999999,
}: ComponentProps) => {
  const equityImg = '//img10.360buyimg.com/imgzone/jfs/t1/197220/36/17666/28241/61946651E1ba1950c/4e9723f9541a2124.png';
  const planImg = require('../assets/7.jpg');
  const defaultValue = {
    prizeKey: null,
    prizeType: 8,
    dayLimitType: 1,
    dayLimit: 1,
    prizeImg: equityImg,
    prizeName: '京东E卡',
  };
  const [prizeData, setPrizeData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, editValue || defaultValue);
  const field = Field.useField();

  const [winJdShow, setWinJdShow] = useState(false); // 京豆详情
  // 发放份数/单次发放量
  const setData = (data: any) => {
    setPrizeData({
      ...prizeData,
      ...data,
    });
  };
  const submit = (values: any, errors: any): void | boolean => {
    if (prizeData.sendTotalCount > prizeData.quantityRemain) {
      Message.error(`发放份数不能大于库存${prizeData.quantityRemain}`);
      return false;
    }
    if (hasLimit && prizeData.dayLimitType === 2 && prizeData.sendTotalCount < prizeData.dayLimit) {
      Message.error(`每日发放限额份数不能大于发放总份数，请重新设置`);
      return false;
    }
    // if (hasProbability && +prizeData.probability <= 0) {
    //   Message.error(`中奖概率必须大于0`);
    //   return false;
    // }
    if (hasProbability && +prizeData.probability + +formData.totalProbability >= 100) {
      Message.error(`中奖总概率不能超过100%`);
      return false;
    }
    !errors && onChange(prizeData);
  };
  const delPlan = () => {
    const data = { ...prizeData };
    data.prizeKey = null;
    data.unitPrice = '';
    data.prizeName = '';
    setPrizeData(data);
  };
  const onSubmit = (resource: any) => {
    if (!resource) {
      return;
    }
    resource.prizeKey = resource.planId;
    resource.unitPrice = resource.faceValue;
    resource.prizeName = resource.planName.substring(0, prizeNameLength);
    setPrizeData(resource);
    setWinJdShow(false);
    field.setErrors({ prizeKey: '', prizeName: '' });
  };
  return (
    <div className={styles.PropertyEcard}>
      <Form field={field} {...prizeFormLayout}>
        <Form.Item required requiredMessage="请选择E卡" style={{ paddingTop: '15px' }}>
          <Input htmlType="hidden" name="prizeKey" value={prizeData.prizeKey} />
          {!prizeData.prizeKey && (
            <div className={styles.selectActivity} style={{ marginTop: 10 }} onClick={() => setWinJdShow(true)}>
              <img style={{ width: '35%' }} src={planImg} alt="" />
            </div>
          )}
          {!!prizeData.prizeKey && (
            <div className={prizeData.planStatus === 2 ? styles.beanPrizeBg : styles.beanPrizeBgy}>
              <img className={styles.prizeBg} src={prizeData.planStatus === 2 ? active : notActive} alt="" />
              {!(activityEditDisabled() && editValue) && (
                <div
                  onClick={() => delPlan()}
                  style={{ backgroundImage: `url(${delIcon})` }}
                  className={styles.delIcon}
                />
              )}
              <div style={{ width: 210 }}>
                <p>京东E卡计划名称：{prizeData.planName}</p>
                <p>京东E卡计划ID：{prizeData.planId}</p>
                <p>创建时间：{format.formatDateTimeDayjs(prizeData.createTime)}</p>
              </div>
              <div style={{ paddingLeft: 60 }}>
                <p>
                  有效期：{format.formatDateTimeDayjs(prizeData.startDate)}至
                  {format.formatDateTimeDayjs(prizeData.endDate)}
                </p>
                <p>面额：{prizeData.faceValue}元</p>
                <p>剩余数量：{prizeData.quantityRemain}份</p>
              </div>
            </div>
          )}
        </Form.Item>

        <Form.Item label="单份价值" required requiredMessage="请输入单份价值" disabled validator={numRegularCheckInt}>
          <NumberPicker
            placeholder="请输入单份价值"
            className={styles.formNumberPicker}
            onChange={(unitPrice: any) => setData({ unitPrice })}
            name="unitPrice"
            type="inline"
            precision={2}
            max={9999999}
            min={0.01}
            value={prizeData.unitPrice}
          />
          元
        </Form.Item>
        <Form.Item label="发放份数" required requiredMessage="请输入发放份数" validator={numRegularCheckInt}>
          <NumberPicker
            placeholder="请输入发放份数"
            className={styles.formNumberPicker}
            type="inline"
            min={1}
            max={sendTotalCountMax}
            step={1}
            name="sendTotalCount"
            value={prizeData.sendTotalCount}
            onChange={(sendTotalCount) => setData({ sendTotalCount, unitCount: 1 })}
          />
          份
        </Form.Item>
        {hasProbability && (
          <Form.Item label="中奖概率" required requiredMessage="请输入中奖概率">
            <Input
              placeholder="请输入中奖概率"
              name="probability"
              value={prizeData.probability}
              addonTextAfter="%"
              onChange={(probability) => {
                if (probability) {
                  const regex = /^(\d|[1-9]\d|100)(\.\d{0,3})?$/;
                  if (regex.test(probability)) {
                    setData({ probability });
                  }
                } else {
                  setData({ probability: '' });
                }
              }}
              className={styles.formInputCtrl}
            />

            <div style={{ marginTop: 5 }}>中奖概率不建议为0%，易引发客诉，请慎重</div>
          </Form.Item>
        )}
        {hasLimit && (
          <Form.Item label="每日限额" required requiredMessage="请输入每日限额">
            {prizeData.dayLimitType === 2 && <Input htmlType="hidden" name="dayLimit" value={prizeData.dayLimit} />}
            <div>
              <Radio.Group
                defaultValue={prizeData.dayLimitType}
                onChange={(dayLimitType) => setData({ dayLimitType, dayLimit: dayLimitType === 1 ? 0 : '' })}
              >
                <Radio id="1" value={1}>
                  不限制
                </Radio>
                <Radio id="2" value={2}>
                  限制
                </Radio>
              </Radio.Group>
              {prizeData.dayLimitType === 2 && (
                <div className={styles.panel}>
                  <NumberPicker
                    placeholder="请输入每日限额"
                    value={prizeData.dayLimit}
                    onChange={(dayLimit) => {
                      setData({ dayLimit });
                      field.setErrors({ dayLimit: '' });
                    }}
                    type="inline"
                    min={1}
                    max={9999999}
                    className={styles.number}
                  />
                  份
                </div>
              )}
            </div>
          </Form.Item>
        )}
        {hasShowTime && (
          <Form.Item label="限制资产展示时间" required>
            <Switch
              checked={prizeData.showTimeType}
              onChange={(showTimeType) => {
                setData({ showTimeType, showRangeDate: '', showStartTime: null, showEndTime: null });
              }}
            />
            {prizeData.showTimeType && (
              <Form.Item required requiredMessage="请选择时间" style={{ marginTop: 10 }}>
                <DatePicker2.RangePicker
                  className="w-300"
                  name="showRangeDate"
                  inputReadOnly
                  hasClear={false}
                  showTime
                  value={prizeData.showRangeDate}
                  onChange={(showRangeDate) => {
                    setData({
                      showRangeDate,
                      showStartTime: showRangeDate[0] ? showRangeDate[0].format(constant.DATE_FORMAT_TEMPLATE) : '',
                      showEndTime: showRangeDate[1] ? showRangeDate[1].format(constant.DATE_FORMAT_TEMPLATE) : '',
                    });
                  }}
                />
              </Form.Item>
            )}
          </Form.Item>
        )}
        <Form.Item label="奖品名称" required requiredMessage="请输入奖品名称">
          <Input
            className={styles.formInputCtrl}
            maxLength={prizeNameLength}
            showLimitHint
            placeholder="请输入奖品名称"
            name="prizeName"
            value={prizeData.prizeName}
            onChange={(prizeName) => setData({ prizeName })}
          />
        </Form.Item>
        <Form.Item label="奖品图片">
          <Grid.Row>
            <Form.Item style={{ marginRight: 10, marginBottom: 0 }}>
              <LzImageSelector
                width={width}
                height={height}
                value={prizeData.prizeImg}
                onChange={(prizeImg) => {
                  setData({ prizeImg });
                }}
              />
            </Form.Item>
            <Form.Item style={{ marginBottom: 0 }}>
              <div className={styles.tip}>
                <p>
                  图片尺寸：{width}px*{height || '任意'}px
                </p>
                <p>图片大小：不超过1M</p>
                <p>图片格式：JPG、JPEG、PNG</p>
              </div>
              <div>
                <Button
                  type="primary"
                  text
                  onClick={() => {
                    setData({ prizeImg: equityImg });
                  }}
                >
                  重置
                </Button>
              </div>
            </Form.Item>
          </Grid.Row>
        </Form.Item>
        <Form.Item>
          <Grid.Row>
            <Grid.Col style={{ textAlign: 'right' }}>
              <Form.Submit className="form-btn" validate type="primary" onClick={submit}>
                确认
              </Form.Submit>
              <Form.Reset className="form-btn" onClick={onCancel}>
                取消
              </Form.Reset>
            </Grid.Col>
          </Grid.Row>
        </Form.Item>
      </Form>
      <Dialog
        title="选择京东E卡"
        footer={false}
        shouldUpdatePosition
        visible={winJdShow}
        onClose={() => setWinJdShow(false)}
      >
        <Plan onSubmit={onSubmit} />
      </Dialog>
    </div>
  );
};

export default PropertyEcard;
