$ERROR_COLOR: #f33;
$WARNING_COLOR: #f90;
$SUCCESS_COLOR: #0b6;
$INFO_COLOR: #39f;
.PropertyGiftCard {
  .panel {
    box-sizing: border-box;
    background: #f5f7f9;
    padding: 15px;
    border: 1px solid #d7dde4;
    margin-top: 15px;
    min-width: 350px;
  }

  .number {
    margin: 0 10px;
    width: 160px !important;
  }

  .formItemExtra {
    padding-top: 10px;
    color: #ff3000;
  }

  .grayFont {
    color: #9ca7b6;
  }

  .selectActivity {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    min-height: 120px;
    border: 1px dashed #d7dde4;
  }

  .recharge {
    margin-left: 10px;
    color: #39ff;
    cursor: pointer;
  }

  .delIcon {
    position: absolute;
    top: 6px;
    right: 6px;
    width: 14px;
    height: 14px;
    cursor: pointer;
  }

  .couponPrizeBg, .beanPrizeBgDis {
    position: relative;
    display: flex;
    //width: 560px;
    padding-bottom: 18px;
    padding-top: 22px;
    padding-left: 32px;
    color: #657180ff;
    line-height: 14px;
    background-color: #e5f9f0ff;
    background-repeat: no-repeat;
    border: 1px solid #0b6f;

    p {
      line-height: 20px;
    }
  }

  .couponPrizeBg, .beanPrizeBg {
    position: relative;
    display: flex;
    padding-bottom: 18px;
    padding-top: 22px;
    padding-left: 32px;
    color: #657180ff;
    line-height: 14px;
    background-color: #e5f9f0ff;
    background-repeat: no-repeat;
    border: 1px solid #0b6f;

    p {
      line-height: 20px;
    }
  }

  .couponPrizeBgy, .beanPrizeBgy {
    position: relative;
    display: flex;
    padding-bottom: 18px;
    padding-top: 22px;
    padding-left: 32px;
    color: #657180ff;
    line-height: 14px;
    background-color: #fff5e5;
    background-repeat: no-repeat;
    border: 1px solid #f90;

    p {
      line-height: 20px;
    }
  }

  .beanPrizeBg {

    margin-top: 20px;
    padding-top: 18px;
  }

  .beanPrizeBgy {

    margin-top: 20px;
    padding-top: 18px;
  }

  .beanPrizeBgDis {

    margin-top: 20px;
    padding-top: 18px;
  }

  .prizeBg {
    position: absolute;
    top: -3px;
    left: -3px;
  }

  .accountColor {
    color: #f90f;
  }

  .formInputCtrl {
    width: 200px !important;
  }

  .formNumberPicker {
    width: 200px !important;
  }

  .formSelectPicker {
    width: 200px !important;
  }

}

.PropertyJdBeanPlan {
  width: 1000px;

  .reminderBox {
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    margin-bottom: 10px;
  }

  .totalStyle {
    color: #9ca7b6;
  }

  .table-status-WARNING_COLOR {
    // 黄色字体
    color: $WARNING_COLOR;
  }

  .table-status-INFO_COLOR {
    // 蓝色字体
    color: $INFO_COLOR;
  }

  .table-status-SUCCESS_COLOR {
    // 绿色字体
    color: $SUCCESS_COLOR;
  }

  .table-status-ERROR_COLOR {
    // 红色字体
    color: $ERROR_COLOR;
  }

  .pagination {
    margin-top: 20px;
  }
}
