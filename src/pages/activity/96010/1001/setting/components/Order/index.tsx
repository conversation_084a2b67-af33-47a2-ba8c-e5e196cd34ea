import React, { useReducer, useImperativeHandle, useEffect, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Grid, Icon, Input, Message, NumberPicker, Radio, Select } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, getShopOrderStartTime, validateActivityThreshold } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';
import ChooseGoods from '@/components/ChooseGoods';
import SkuList from '@/components/SkuList';
import LzToolTip from '@/components/LzToolTip';
import { getNotExcludedSku } from '@/api/sku';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const RadioGroup = Radio.Group;
const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [
  { label: '已付款', value: 0 },
  { label: '已完成', value: 1 },
];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: dayjs(formData.endTime).subtract(180, 'days').startOf('day').valueOf(),
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRestrainRangeData');
    }, 1000);
  }, [formData.endTime]);
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    if (
      format.formatDateTimeDayjs(orderRestrainRangeData[0]) === format.formatDateTimeDayjs(orderRestrainRangeData[1])
    ) {
      setData({
        orderRestrainRangeData: [orderRestrainRangeData[0], ''],
        orderRestrainStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
        orderRestrainEndTime: format.formatDateTimeDayjs(''),
      });
      Message.error('开始时间和结束时间不能相同');
      return;
    }
    setData({
      orderRestrainRangeData,
      orderRestrainStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderRestrainEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };
  const handleSkuChange = (data) => {
    setData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    formData.orderSkuList.splice(index, 1);
    setData({ orderSkuList: formData.orderSkuList });
  };

  // const radioDisabled = () => {
  //   if (formData.orderRestrainStatus === 0) {
  //     return true;
  //   } else {
  //     return false;
  //   }
  // };

  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    // const { orderRestrainStartTime, orderRestrainEndTime } = formData;
    const orderRestrainStartTime = val[0];
    const orderRestrainEndTime = val[1];
    if (!orderRestrainStartTime || !orderRestrainEndTime) {
      callback('请选下单时间');
    } else if (
      !shopOrderInfo.longTermOrder &&
      dayjs(orderRestrainStartTime).valueOf() < shopOrderInfo.shopOrderStartTime
    ) {
      callback(`下单时间不能早于活动结束时间前${shopOrderInfo.orderRetentionDays}天`);
    } else if (dayjs(orderRestrainEndTime).startOf('s').isAfter(dayjs(formData.endTime))) {
      callback('下单时间不能晚于活动结束时间');
    } else {
      callback();
    }
  };

  const handleStatusChange = (orderRestrainStatus) => {
    setData({ orderRestrainStatus });
    if (orderRestrainStatus === 0 && formData.isDelayedDisttribution === 1) {
      setData({ orderRestrainStatus: 0, isDelayedDisttribution: 0, awardDays: 0 });
    }
  };

  const handlePreview = async (data) => {
    if (formData.orderSkuisExposure === 1) {
      setData({ orderSkuListPreview: data });
    }
    if (formData.orderSkuisExposure === 2 && data.length) {
      const skuIdsList = formData.orderSkuList.map((item) => item.skuId);
      // 调接口查排除商品
      await getNotExcludedSku({ skuIds: skuIdsList })
        .then((res: any): void => {
          console.log(res);
          setData({ orderSkuListPreview: res });
        })
        .catch((e) => {
          console.log(e.message);
        });
    }
  };

  const MoveTarget = <Icon type="help" id="top" style={{ marginRight: '10px' }} />;

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="订单限制" disabled={false}>
            <Radio.Group
              value={formData.limitOrder}
              onChange={(limitOrder) => {
                setData({ limitOrder });
              }}
            >
              {/* <Radio value={0}>不限制</Radio> */}
              <Radio value={1}>限制</Radio>
            </Radio.Group>
          </FormItem>
          {!!formData.limitOrder && (
            <>
              <FormItem label="下单时间" required requiredMessage="请选下单时间" validator={validateOrderTime}>
                <RangePicker
                  className="w-300"
                  name="orderRestrainRangeData"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  value={
                    formData.orderRestrainRangeData || [
                      new Date(formData.orderRestrainStartTime),
                      new Date(formData.orderRestrainEndTime),
                    ]
                  }
                  onChange={onDataRangeChange}
                  disabledDate={(date) => {
                    return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
                  }}
                />
                <div className={styles.tip}>
                  注：1、默认支持查询
                  {shopOrderInfo.longTermOrder
                    ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                    : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
                  的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
                  <br />
                  2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。
                </div>
              </FormItem>
              <FormItem label="订单状态" disabled>
                <Select dataSource={ORDER_STATUS} value={formData.orderRestrainStatus} onChange={handleStatusChange} />
              </FormItem>
              <FormItem label="订单笔数" required>
                <FormItem>
                  <Radio.Group
                    value={formData.orderStrokeStatus}
                    onChange={(orderStrokeStatus) => setData({ orderStrokeStatus })}
                  >
                    <Radio value={1}>单笔</Radio>
                    <Radio value={2}>多笔</Radio>
                  </Radio.Group>
                </FormItem>
                {formData.orderStrokeStatus === 2 && (
                  <FormItem required requiredMessage="请输入订单笔数">
                    大于等于{' '}
                    <NumberPicker
                      name="orderStrokeCount"
                      min={1}
                      max={5}
                      type="inline"
                      value={formData.orderStrokeCount}
                      onChange={(orderStrokeCount: number) => setData({ orderStrokeCount })}
                    />{' '}
                    笔
                  </FormItem>
                )}
              </FormItem>
              {/* <FormItem label="奖品延迟发放" required> */}
              {/*  <FormItem> */}
              {/*    <Radio.Group */}
              {/*      value={formData.isDelayedDisttribution} */}
              {/*      onChange={(isDelayedDisttribution) => */}
              {/*        setData({ isDelayedDisttribution, awardDays: isDelayedDisttribution }) */}
              {/*      } */}
              {/*    > */}
              {/*      <Radio value={0}>否</Radio> */}
              {/*      <Radio value={1} disabled={radioDisabled()}> */}
              {/*        是 */}
              {/*      </Radio> */}
              {/*    </Radio.Group> */}
              {/*  </FormItem> */}
              {/*  {formData.isDelayedDisttribution === 1 && formData.orderRestrainStatus === 1 && ( */}
              {/*    <FormItem required requiredMessage="请输入延迟天数"> */}
              {/*      延迟发放{' '} */}
              {/*      <NumberPicker */}
              {/*        name="awardDays" */}
              {/*        min={1} */}
              {/*        max={99} */}
              {/*        type="inline" */}
              {/*        value={formData.awardDays} */}
              {/*        onChange={(awardDays: number) => setData({ awardDays })} */}
              {/*      />{' '} */}
              {/*      天 */}
              {/*    </FormItem> */}
              {/*  )} */}
              {/* </FormItem> */}
              <FormItem label="订单拆单后是否合并" required>
                <RadioGroup
                  value={formData.split}
                  onChange={(split: number) => {
                    setData({ split });
                  }}
                >
                  <Radio id="1" value={1}>
                    是
                  </Radio>
                  <Radio id="0" value={0}>
                    否
                  </Radio>
                </RadioGroup>
                <LzToolTip
                  content={
                    <div>
                      <div>
                        <p>拆单：因平台因素，用户的某笔总的父订单会有被拆单成多笔子订单发货的情况。</p>
                        <br />
                        当用户订单在平台上被拆分为多笔子订单时，可以选择以下方式来判断活动参与门槛： <br />
                        • 选择“是”：按照父订单（将本店铺的子订单合并后的订单）来判断参与门槛。 <br />
                        • 选择“否”：按照拆单后的子订单（包括父订单）来判断参与门槛。 <br />
                      </div>
                    </div>
                  }
                />
              </FormItem>
              <FormItem label="价格类型">
                <Radio.Group value={formData.priceType} onChange={(val) => setData({ priceType: val })}>
                  <Radio value={0}>京东价</Radio>
                  {/* <Radio value={1} disabled={!isPopShop()}> */}
                  {/*  实付价 */}
                  {/*  /!* <span className={styles.tip}>(优惠券、京豆、红包、礼品卡、储值卡、储值金、E卡不计入实付价)</span> *!/ */}
                  {/* </Radio> */}
                </Radio.Group>
                {/* <ActualPaymentPriceDesc /> */}
              </FormItem>
              <FormItem
                label="订单金额"
                required
                requiredMessage="请输入订单金额"
                extra={<div className="next-form-item-help">注：若选择指定商品，则订单中指定商品金额需满足所设值</div>}
              >
                大于等于{' '}
                <NumberPicker
                  precision={2}
                  name={'orderRestrainAmount'}
                  min={0}
                  style={{ width: '150px' }}
                  max={9999999}
                  type="inline"
                  value={formData.orderRestrainAmount}
                  onChange={(orderRestrainAmount: number) => setData({ orderRestrainAmount })}
                />{' '}
                元
              </FormItem>
              <FormItem label="订单商品" required>
                <RadioGroup
                  value={formData.orderSkuisExposure}
                  onChange={(orderSkuisExposure: number) => {
                    setData({
                      orderSkuisExposure,
                      orderSkuList: [],
                      orderSkuListPreview: [],
                    });
                  }}
                >
                  <Radio id="0" value={0}>
                    全部商品<span className={styles.tip}>(不含虚拟商品)</span>
                  </Radio>
                  <Radio id="1" value={1}>
                    指定商品
                  </Radio>
                  <Radio id="2" value={2}>
                    排除商品
                  </Radio>
                </RadioGroup>
                <Grid.Row>
                  {(formData.orderSkuisExposure === 1 || formData.orderSkuisExposure === 2) && (
                    <FormItem
                      name="orderSkuList"
                      required
                      requiredMessage={'请选择订单商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <Input
                        className="validateInput"
                        name="orderSkuList"
                        value={formData.orderSkuList.length ? 1 : ''}
                      />
                      <ChooseGoods
                        max={formData.orderSkuisExposure === 2 ? 100 : 0}
                        value={formData.orderSkuList}
                        onChange={handleSkuChange}
                        sRef={null}
                      />
                      <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={removeSku} />
                      <p className={styles.tip}>注：商品价格每天凌晨同步;</p>
                    </FormItem>
                  )}
                </Grid.Row>
              </FormItem>
            </>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
