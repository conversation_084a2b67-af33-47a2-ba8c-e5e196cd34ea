import React, { useReducer, useState, useEffect, useImperativeHandle } from 'react';
import { Form, Field, Table, Button, Dialog, Message } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '../ChoosePrizeForDZ';
import { activityEditDisabled, getParams, isDisableSetPrize } from '@/utils';
import { FormLayout, PageData, PRIZE_TYPE, PRIZE_INFO, PrizeInfo } from '../../../util';
import LzImageSelector from '@/components/LzImageSelector';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const operationType: string = getParams('type');
  const isEdit: boolean = operationType === 'edit';
  const isDraft: boolean = operationType === 'draft';
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  const [bottomVisible, setBottomVisible] = useState(false);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  const [bottomEditValue, setBottomEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  const [bottomTarget, setBottomTarget] = useState(0);
  // 是否刚刚添加了一个新奖品
  const [hasAddNewPrize, setHasAddNewPrize] = useState(false);
  const [bottomHasAddNewPrize, setBottomHasAddNewPrize] = useState(false);

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.mainPrizeList[target] = data;
    formData.mainPrizeList[target].status = 1;
    formData.mainPrizeList[target].sortId = target;
    setData(formData);
    setVisible(false);
  };

  /**
   * 新建/编辑兜底奖品回调
   * @param data
   */
  const onBottomPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    formData.bottomPrizeList[bottomTarget] = data;
    setData(formData);
    setBottomVisible(false);
  };

  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  const onBottomCancel = (): void => {
    setBottomVisible(false);
  };

  return (
    <div>
      <LzPanel title="奖品设置">
        <Form {...formItemLayout} field={field}>
          <FormItem label="奖品列表" required>
            <Table dataSource={formData.mainPrizeList} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : '0'}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => <div>{row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0}</div>}
              />
              <Table.Column
                title="中奖弹窗内奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="主页展示奖品图"
                cell={(_, index, row) => (
                  <LzImageSelector
                    width={200}
                    height={200}
                    value={row.topShowImg}
                    onChange={() => {
                      setData(formData.mainPrizeList[index].topShowImg);
                    }}
                  />
                )}
              />
              <Table.Column
                title="奖品状态"
                cell={(_, index, row) => (
                  <div style={{ color: row.status === 1 ? 'green' : 'red' }}>
                    {row && row.prizeName === '' ? '-' : row && row.status === 1 ? '已启用' : '已下架'}
                  </div>
                )}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, row) => {
                  // 当isDraft时，如果本行奖品不为空且不是本次新添加的，禁用编辑和删除按钮；编辑的时候（isEdit）如果本行奖品不为空且不是本次新添加的，禁用编辑和删除按钮
                  const isCurrentlyAdding = hasAddNewPrize && index === target;
                  const isDisabled = isDraft || isEdit ? row.prizeName !== '' && !isCurrentlyAdding : false;

                  return (
                    <FormItem style={{ paddingTop: '18px' }} disabled={isDisabled}>
                      <Button
                        text
                        type="primary"
                        disabled={isDisabled}
                        onClick={() => {
                          const rowData = formData.mainPrizeList[index];
                          if (rowData.prizeName === '') {
                            setEditValue(null);
                          } else {
                            setEditValue(rowData);
                          }
                          setTarget(index);
                          setVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        text
                        type="primary"
                        disabled={isDisabled}
                        onClick={() => {
                          if (row.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该奖品？',
                              onOk: () => {
                                // 真正删除行，而不是替换为空奖品
                                formData.mainPrizeList.splice(index, 1);
                                // 如果删除的是当前新添加的行，重置状态
                                if (hasAddNewPrize && index === target) {
                                  setHasAddNewPrize(false);
                                }
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          } else {
                            // 删除新添加的空奖品行
                            formData.mainPrizeList.splice(index, 1);
                            setHasAddNewPrize(false);
                            if (index > 0) {
                              formData.mainPrizeList[index - 1].status = 1;
                            }
                            setData(formData);
                          }
                        }}
                      >
                        删除
                      </Button>
                      {row.status === 1 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认下架该奖品？',
                              onOk: () => {
                                formData.mainPrizeList[index].status = 0;
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          下架
                        </Button>
                      )}
                      {row.status === 0 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认上架该奖品？',
                              onOk: () => {
                                formData.mainPrizeList[index].status = 1;
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          }}
                        >
                          上架
                        </Button>
                      )}
                    </FormItem>
                  );
                }}
              />
            </Table>
            <Button
              type="secondary"
              style={{ marginTop: 10 }}
              onClick={() => {
                // 奖品列表为空时，直接添加一个启用的奖品
                formData.mainPrizeList.push(PRIZE_INFO);
                setHasAddNewPrize(true);
                setTarget(formData.mainPrizeList.length - 1);
                setVisible(true);
              }}
            >
              添加奖品
            </Button>
          </FormItem>
          <FormItem label="兜底奖品列表" required {...formItemLayout}>
            <Table dataSource={formData.bottomPrizeList} style={{ marginTop: '15px' }}>
              <Table.Column title="奖项" cell={(_, index) => <div>{index + 1}</div>} />
              <Table.Column title="奖项名称" dataIndex="prizeName" />
              <Table.Column
                title="奖项类型"
                cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                dataIndex="prizeType"
              />
              <Table.Column
                title="单位数量"
                cell={(_, index, row) => {
                  if (row.prizeType === 1) {
                    return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                  } else {
                    return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                  }
                }}
              />
              <Table.Column
                title="发放份数"
                cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
              />
              <Table.Column
                title="单份价值(元)"
                cell={(_, index, row) => (
                  <div>{row.sendTotalCount ? (row.unitPrice ? Number(row.unitPrice).toFixed(2) : 0) : ''}</div>
                )}
              />
              <Table.Column
                title="中奖弹窗内奖品图"
                cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
              />
              <Table.Column
                title="主页展示奖品图"
                cell={(_, index, row) => (
                  <LzImageSelector
                    width={200}
                    height={200}
                    value={row.topShowImg}
                    onChange={() => {
                      setData(formData.bottomPrizeList[index].topShowImg);
                    }}
                  />
                )}
              />
              <Table.Column
                title="操作"
                width={130}
                cell={(val, index, row) => {
                  // 当isDraft时，如果本行奖品不为空且不是本次新添加的，禁用编辑和删除按钮；编辑的时候（isEdit）如果本行奖品不为空且不是本次新添加的，禁用编辑和删除按钮
                  const isCurrentlyAdding = hasAddNewPrize && index === target;
                  const isDisabled = isDraft || isEdit ? row.prizeName !== '' && !isCurrentlyAdding : false;

                  return (
                    <FormItem style={{ paddingTop: '18px' }} disabled={isDisabled}>
                      <Button
                        text
                        type="primary"
                        disabled={isDisabled}
                        onClick={() => {
                          const rowData = formData.bottomPrizeList[index];
                          if (rowData.prizeName === '') {
                            setBottomEditValue(null);
                          } else {
                            setBottomEditValue(rowData);
                          }
                          setBottomTarget(index);
                          setBottomVisible(true);
                        }}
                      >
                        编辑
                      </Button>
                      <Button
                        text
                        type="primary"
                        disabled={isDisabled}
                        onClick={() => {
                          if (row.prizeType) {
                            Dialog.confirm({
                              v2: true,
                              title: '提示',
                              centered: true,
                              content: '确认删除该兜底奖品？',
                              onOk: () => {
                                // 真正删除行，而不是替换为空奖品
                                formData.bottomPrizeList.splice(index, 1);
                                // 如果删除的是当前新添加的行，重置状态
                                if (bottomHasAddNewPrize && index === bottomTarget) {
                                  setBottomHasAddNewPrize(false);
                                }
                                setData(formData);
                              },
                              onCancel: () => console.log('cancel'),
                            } as any);
                          } else {
                            // 删除新添加的空奖品行
                            formData.bottomPrizeList.splice(index, 1);
                            setBottomHasAddNewPrize(false);
                            if (index > 0) {
                              formData.bottomPrizeList[index - 1].status = 1;
                            }
                            setData(formData);
                          }
                        }}
                      >
                        删除
                      </Button>
                    </FormItem>
                  );
                }}
              />
            </Table>
            <Button
              type="secondary"
              style={{ marginTop: 10 }}
              onClick={() => {
                // 奖品列表为空时，直接添加一个启用的奖品
                formData.bottomPrizeList.push(PRIZE_INFO);
                setBottomHasAddNewPrize(true);
                setBottomTarget(formData.bottomPrizeList.length - 1);
                setBottomVisible(true);
              }}
            >
              添加兜底奖品
            </Button>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasLimit={false}
          typeList={[1, 2, 3, 7]}
          defaultTarget={2}
          hasProbability={false}
          defaultEditValue={null}
        />
      </LzDialog>

      <LzDialog
        title={false}
        visible={bottomVisible}
        footer={false}
        onClose={() => setBottomVisible(false)}
        style={{ width: '670px' }}
      >
        <ChoosePrize
          formData={formData}
          editValue={bottomEditValue}
          onChange={onBottomPrizeChange}
          onCancel={onBottomCancel}
          hasLimit={false}
          typeList={[4]}
          defaultTarget={4}
          hasProbability={false}
          defaultEditValue={null}
        />
      </LzDialog>
    </div>
  );
};
