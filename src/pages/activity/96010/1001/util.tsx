import React from 'react';
import dayjs, { Dayjs } from 'dayjs';
import { Message } from '@alifd/next';
import { getParams } from '@/utils';
import Preview from '@/components/LzCrowdBag/preview';

export interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export interface PrizeInfo {
  prizeName: string;
  prizeImg: string;
  prizeType: number;
  probability: number | string;
  unitCount: number;
  unitPrice: number;
  sendTotalCount: number;
  dayLimit: number;
  dayLimitType: number;
  ifPlan: number;
  status: number;
  startTime?: string;
  endTime?: string;
  startDate?: string;
  endDate?: string;
}
export interface CustomValue {
  actBg: string;
  pageBg: string;
  actBgColor: string;
  shopNameColor: string;
  btnColor: string;
  btnBg: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  blindBoxBg: string;
  drawBtn: string;
  grayDrawBtn: string;
  blindBox1: string;
  blindBox1Light: string;
  blindGif: string;
  winnersDanMu: string;
  prizeRemainNum: string;
}
export interface PageData {
  activityName: string;
  rangeDate: [Dayjs, Dayjs];
  startTime: string;
  endTime: string;
  threshold: number;
  supportLevels: string;
  limitJoinTimeType: number;
  joinTimeRange: [string, string] | [Dayjs, Dayjs];
  joinStartTime: string;
  joinEndTime: string;
  partake: number;
  partakeRangeData: [string, string] | null;
  partakeStartTime: string;
  partakeEndTime: string;
  crowdPackage: number;
  limitOrder: number;
  awardDays: number;
  split: number;
  priceType: number;
  isDelayedDisttribution: number;
  orderRestrainRangeData: [Dayjs, Dayjs];
  orderRestrainStartTime: string;
  orderRestrainEndTime: string;
  orderRestrainStatus: number;
  orderRestrainAmount: string;
  orderStrokeStatus: number;
  orderStrokeCount: number;
  orderSkuisExposure: number;
  orderSkuList: any[];
  chance: number;
  crowdBag: any;
  totalProbability: number;
  mainPrizeList: PrizeInfo[]; // 根据实际情况，可能需要定义奖品的类型
  bottomPrizeList: PrizeInfo[];
  // taskList: any[]; // 根据实际情况，可能需要定义任务的类型
  winLotteryDayType: number;
  winLotteryDayCounts: number;
  winLotteryTotalType: number;
  winLotteryTotalCounts: number;
  isExposure: number;
  skuList: any[];
  shareStatus: number;
  shareTitle: string;
  rules: string;
  templateCode: string;
  cmdImg: string;
  h5Img: string;
  mpImg: string;
  gradeLabel: string[];
}
interface PrizeType {
  [key: number]: string;
}
export const PRIZE_TYPE: PrizeType = {
  12: '京元宝权益',
  2: '京豆',
  1: '优惠券',
  3: '实物',
  4: '积分',
  6: '红包',
  7: '礼品卡',
  8: '京东E卡',
  9: 'PLUS会员卡',
  10: '爱奇艺会员卡',
};

// form格式
export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
// 步骤文案
export const STEPS = ['① 设置模板', '② 活动设置', '③ 活动预览', '④ 活动完成'];
// 装修默认数据
export const DEFAULT_CUSTOM_VALUE: CustomValue = {
  // 活动主图
  actBg: '',
  // 页面背景图
  pageBg: '',
  // 页面背景颜色
  actBgColor: '',
  // 文字颜色
  shopNameColor: '',
  // 按钮
  btnColor: '',
  btnBg: '',
  cmdImg: '',
  h5Img: '',
  mpImg: '',
  blindBoxBg: '', // 盲盒背景
  drawBtn: '', // 拆盲盒按钮
  grayDrawBtn: '', // 拆盲盒不可点击按钮
  blindBox1: '', // 盲盒默认图片1
  blindBox1Light: '', // 盲盒选中默认图片1
  blindGif: '', // 选中按钮
  winnersDanMu: '', // 弹幕条
  prizeRemainNum: '',
};
// 活动设置默认数据

export const INIT_PAGE_DATA = (): PageData => {
  return {
    // 活动名称
    activityName: `伊利100%盲盒-${dayjs().format('YYYY-MM-DD')}`,
    // 日期区间（不提交）
    rangeDate: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],
    // 活动开始时间
    startTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 活动结束时间
    endTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 活动门槛（不提交）
    threshold: 1,
    // 支持的会员等级(-1:无门槛；1,2,3,4,5,-9以逗号做分割(-9为关注店铺用户))
    supportLevels: '1,2,3,4,5',
    // 限制入会时间 0：不限制；1：入会时间
    limitJoinTimeType: 0,
    // 入会时间
    joinTimeRange: [] as any,
    joinStartTime: '',
    joinEndTime: '',
    // 参与时段限制（不提交）
    partake: 0,
    // 参与时段限制（不提交）
    partakeRangeData: null,
    // 参与开始时间
    partakeStartTime: '',
    // 参与结束时间
    partakeEndTime: '',
    // 参与者生成人群包
    crowdPackage: 0,
    // 是否延迟发奖
    isDelayedDisttribution: 0,
    // 延迟延迟发奖天数
    awardDays: 0,
    // 是否拆单0否1是
    split: 1,
    // 价格类型
    priceType: 0,
    // 限制订单
    limitOrder: 1,
    // 限制订单时间
    orderRestrainRangeData: [
      (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
      (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    ],

    // 限制订单开始时间
    orderRestrainStartTime: (dayjs().add(0, 'day') as any).format('YYYY-MM-DD 00:00:00'),
    // 限制订单结束时间
    orderRestrainEndTime: (dayjs().add(30, 'day') as any).format('YYYY-MM-DD 23:59:59'),
    // 限制订单状态
    orderRestrainStatus: 1,
    // 限制订单金额
    orderRestrainAmount: '',
    // 订单笔数类型 1单笔2 多笔
    orderStrokeStatus: 1,
    // 订单笔数
    orderStrokeCount: 1,
    // 订单商品全点商品或指定商品
    orderSkuisExposure: 0,
    // 限制订单商品列表
    orderSkuList: [],
    // 抽奖机会数
    chance: 1,
    // 中奖总概率
    totalProbability: 0,
    // 奖品列表
    mainPrizeList: [],
    // 兜底奖品列表
    bottomPrizeList: [],
    // 是否限制每人每天中奖次数 1 不限制 2 限制
    winLotteryDayType: 1,
    // 每人每天中奖次数
    winLotteryDayCounts: 1,
    // 是否限制每人累计次数 1 不限制 2 限制
    winLotteryTotalType: 1,
    // 每人累计中奖次数
    winLotteryTotalCounts: 1,
    // 是否开启曝光
    isExposure: 1,
    // 商品列表
    skuList: [],
    // 是否开启分享
    shareStatus: 1,
    // 分享标题
    shareTitle: '下单即有机会抽取好礼，快来看看吧！',
    // 分享图片
    cmdImg: '',
    h5Img: '',
    mpImg: '',
    // 活动规则
    rules: '',
    templateCode: '',
    gradeLabel: [],
    crowdBag: null,
  };
};

export const PRIZE_INFO: PrizeInfo = {
  // 奖品名称
  prizeName: '谢谢参与',
  // 奖品图
  prizeImg: '',
  // 奖品类型
  prizeType: 0,
  // 中奖概率
  probability: 0,
  // 单位数量
  unitCount: 0,
  // 单位价值
  unitPrice: 0,
  // 发放份数
  sendTotalCount: 0,
  // 每日发放限额
  dayLimit: 0,
  // 每日发放限额类型 1 不限制 2限制
  dayLimitType: 1,
  ifPlan: 0,
  // 上架下架状态 1 上架 0 下架
  status: 1,
};
// 预览二维码大小
export const QRCODE_SIZE = 74;
// 预览二维码配置项
export const QRCODE_SETTING = {
  src: require('@/assets/images/jd-logo.webp'),
  height: QRCODE_SIZE / 7.4,
  width: QRCODE_SIZE / 7.4,
  excavate: false,
};

// 生成会员等级字符串
export const generateMembershipString = (formData: PageData, type?: string) => {
  if (formData.threshold === 1) {
    const levels = formData.supportLevels.split(',');
    const LEVEL_MAP = formData.gradeLabel;
    const memberLevelsList = levels.filter((e) => Number(e) > 0);
    const membershipLevels = memberLevelsList.map((l, index) => LEVEL_MAP[index]).join('，');
    const membershipString = memberLevelsList.length ? `店铺会员（${membershipLevels}）` : '';
    const extraLevelsString = levels
      .filter((e) => Number(e) < 0)
      .map((l, index) => LEVEL_MAP[index + memberLevelsList.length])
      .join('、');

    const joinTimeLimitString =
      formData.limitJoinTimeType === 1 ? `。限制入会时间：${formData.joinStartTime}至${formData.joinEndTime}` : '';

    return (
      membershipString +
      (extraLevelsString && `${memberLevelsList.length > 0 ? '、' : ''}${extraLevelsString}`) +
      joinTimeLimitString
    );
  }
  if (formData.threshold === 2) {
    return !type ? <Preview value={formData.crowdBag} /> : `人群包<${formData.crowdBag.packName}>`;
  }
  return '不限制';
};
// 是否未编辑
const isProcessingEditType = (): boolean => {
  return getParams('type') === 'edit';
};
// 校验开始时间是否符合规则
const isStartTimeValid = (startTime: string): boolean => {
  const isLessThanTenMinutes: boolean = dayjs(startTime).isBefore(dayjs().startOf('day'));
  if (isLessThanTenMinutes) {
    Message.error('活动开始时间应大于当天时间');
    return false;
  }
  return true;
};
// 校验结束时间是否符合规则
const isEndTimeValid = (startTime: string, endTime: string): boolean => {
  const isGreaterThanNow: boolean = dayjs(endTime).isAfter(dayjs());
  const isGreaterThanStart: boolean = dayjs(endTime).isAfter(dayjs(startTime));

  if (!isGreaterThanStart) {
    Message.error('活动结束时间应大于活动开始时间');
    return false;
  }
  if (!isGreaterThanNow) {
    Message.error('活动结束时间应大于当前时间');
    return false;
  }
  return true;
};
// 校验奖品时间是否符合规则
const isPrizeValid = (prize: PrizeInfo, formData: PageData): boolean => {
  const type = prize.prizeType;
  // 0-谢谢参与 3-实物 4-积分 没有时间直接通过
  if (type === 0 || type === 3 || type === 4) {
    return true;
  }
  // 开始时间
  const start = prize.startTime || prize.startDate;
  // 结束时间
  const end = prize.endTime || prize.endDate;

  if (start && end) {
    // 奖品的开始时间间是否小于活动的开始时间
    const isStart: boolean = dayjs(formData.startTime).isBefore(dayjs(start));
    if (isStart) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}起始时间需要早于或等于活动开始时间`);

      return false;
    }
    // 奖品的结束时间间是否大于活动的结束时间
    const isEnd: boolean = dayjs(formData.endTime).isAfter(dayjs(end));
    if (isEnd) {
      Message.error(`奖品${PRIZE_TYPE[prize.prizeType]}结束时间需要晚于或等于活动结束时间`);
      return false;
    }
  }
  return true;
};
// 校验奖品时间是否符合规则
const arePrizesValid = (mainPrizeList: PrizeInfo[], formData: PageData): boolean => {
  for (let i = 0; i < mainPrizeList.length; i++) {
    if (!isPrizeValid(mainPrizeList[i], formData)) {
      return false;
    }
  }
  return true;
};

const hasPrize = (mainPrizeList: PrizeInfo[]): boolean => {
  if (!mainPrizeList.length) {
    Message.error('请设置奖品');
    return false;
  }
  return true;
};

const checkRangeTime = (formData: PageData): boolean => {
  if (formData.partake === 1) {
    if (!formData.partakeStartTime) {
      Message.error('请设置参与开始时间');
      return false;
    }
    if (!formData.partakeEndTime) {
      Message.error('请设置参与结束时间');
      return false;
    }
  }
  return true;
};

const winCounts = (formData: PageData): boolean => {
  if (
    formData.winLotteryTotalType === 2 &&
    formData.winLotteryDayType === 2 &&
    formData.winLotteryDayCounts > formData.winLotteryTotalCounts
  ) {
    Message.error('每日中奖次数不能大于累计中奖次数');
    return false;
  } else if (formData.winLotteryDayType === 2 && (!formData.winLotteryDayCounts || !formData.winLotteryTotalCounts)) {
    Message.error('请配置中奖限制');
    return false;
  }
  return true;
};
export const checkActivityData = (mainPrizeList: PrizeInfo[], formData: PageData): boolean => {
  // 编辑直接通过，不走校验
  if (isProcessingEditType()) {
    return true;
  }
  // 没有选择奖品
  if (!hasPrize(mainPrizeList)) {
    return false;
  }
  // 开始时间异常
  if (!isStartTimeValid(formData.startTime)) {
    return false;
  }
  // 结束时间异常
  if (!isEndTimeValid(formData.startTime, formData.endTime)) {
    return false;
  }
  // 开启但未设置参与时间段
  if (!checkRangeTime(formData)) {
    return false;
  }
  // 奖品时间与活动时间冲突
  if (!arePrizesValid(mainPrizeList, formData)) {
    return false;
  }
  // 中奖次数限制
  if (!winCounts(formData)) {
    return false;
  }
  return true;
};
