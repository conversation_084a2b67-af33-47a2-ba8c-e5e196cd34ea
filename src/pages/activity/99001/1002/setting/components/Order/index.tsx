import React, { useEffect, useImperativeHandle, useReducer, useState } from 'react';
import LzPanel from '@/components/LzPanel';
import { DatePicker2, Field, Form, Grid, Input, NumberPicker, Radio, Select } from '@alifd/next';
import { FormLayout, PageData } from '../../../util';
import { activityEditDisabled, getShopOrderStartTime, validateActivityThreshold } from '@/utils';
import dayjs from 'dayjs';
import constant from '@/utils/constant';
import format from '@/utils/format';
import styles from '../../style.module.scss';
import ChooseGoods from '@/components/ChooseGoods';
import SkuList from '@/components/SkuList';

const dateFormat: string = constant.DATE_FORMAT_TEMPLATE;

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

const ORDER_STATUS = [{ label: '已完成', value: 'FINISH' }];

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onChange, defaultValue, value, sRef }: Props) => {
  // 用于 Fusion Form 表单校验
  const field: Field = Field.useField();
  // 同装修收集数据
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);
  // 同步活动设置数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };
  const [shopOrderInfo, setShopOrderInfo] = useState({
    shopOrderStartTime: dayjs(formData.endTime).subtract(180, 'days').startOf('day').valueOf(),
    longTermOrder: false,
    orderRetentionDays: 180,
  });
  useEffect(() => {
    getShopOrderStartTime().then((res) => {
      if (res.longTermOrder) {
        setShopOrderInfo({
          shopOrderStartTime: res.shopOrderStartTime,
          longTermOrder: res.longTermOrder,
          orderRetentionDays: shopOrderInfo.orderRetentionDays,
        });
      } else {
        setShopOrderInfo({
          shopOrderStartTime: dayjs(formData.endTime).subtract(res.shopOrderStartTime, 'days').startOf('day').valueOf(),
          longTermOrder: res.longTermOrder,
          orderRetentionDays: res.shopOrderStartTime,
        });
      }
    });
    setTimeout(() => {
      field.validate('orderRestrainRangeData');
    }, 1000);
  }, [formData.endTime]);
  // 日期改变，处理提交数据
  const onDataRangeChange = (orderRestrainRangeData): void => {
    setData({
      orderRestrainRangeData,
      orderStartTime: format.formatDateTimeDayjs(orderRestrainRangeData[0]),
      orderEndTime: format.formatDateTimeDayjs(orderRestrainRangeData[1]),
    });
  };
  const handleSkuChange = (data) => {
    setData({ orderSkuList: data });
    field.setErrors({ orderSkuList: '' });
  };
  const removeSku = (index: number) => (): void => {
    formData.orderSkuList.splice(index, 1);
    setData({ orderSkuList: formData.orderSkuList });
  };

  // 下单时间校验
  const validateOrderTime = (rule, val, callback): void => {
    // const { orderStartTime, orderEndTime } = formData;
    const orderStartTime = val[0];
    const orderEndTime = val[1];
    if (orderStartTime && orderEndTime) {
      const diff = dayjs(orderEndTime).diff(dayjs(orderStartTime), 'day');
      if (diff > 100) {
        callback('订单验证时间不能超过100天');
      } else {
        callback();
      }
    } else {
      callback('请选下单时间');
    }
  };

  // 通过Ref触发表单校验
  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: any = null;

      field.validate((errors): void => {
        err = errors;
      });
      return validateActivityThreshold(formData, err, field);
    },
  }));

  // const radioDisabled = () => {
  //   if (formData.orderStatus === 0) {
  //     return true;
  //   } else {
  //     return false;
  //   }
  // };
  const handleStatusChange = (orderStatus) => {
    setData({ orderStatus });
    if (orderStatus === 0 && formData.isDelayedDisttribution === 1) {
      setData({ orderStatus: 0, isDelayedDisttribution: 0 });
    }
  };

  // 订单笔数切换
  const handleOederCountChange = (val) => {
    console.log('orderStrokeCount', val);
    setData({ orderStrokeCount: val });
    if (val === 1) {
      setData({ orderStrokeCount: val, orderStrokeNum: 1 });
    }
  };
  const handlePreview = (data) => {
    setData({ orderSkuListPreview: data });
  };
  return (
    <div>
      <LzPanel title="订单限制">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem label="订单限制" disabled={false}>
            <Radio.Group
              value={formData.orderLimit}
              onChange={(orderLimit) => {
                setData({ orderLimit });
              }}
            >
              {/* <Radio value={0}>不限制</Radio> */}
              <Radio value={1}>限制</Radio>
            </Radio.Group>
          </FormItem>
          {!!formData.orderLimit && (
            <>
              <FormItem label="下单时间" required requiredMessage="请选下单时间" validator={validateOrderTime}>
                <RangePicker
                  className="w-300"
                  name="orderRestrainRangeData"
                  inputReadOnly
                  format={dateFormat}
                  hasClear={false}
                  showTime
                  value={
                    formData.orderRestrainRangeData || [
                      new Date(formData.orderStartTime),
                      new Date(formData.orderEndTime),
                    ]
                  }
                  onChange={onDataRangeChange}
                  disabledDate={(date) => {
                    return date.valueOf() < dayjs(shopOrderInfo.shopOrderStartTime).startOf('day').valueOf();
                  }}
                />
                <div className={styles.tip}>
                  注：1、默认支持查询
                  {shopOrderInfo.longTermOrder
                    ? `${dayjs(shopOrderInfo.shopOrderStartTime).format('YYYY-MM-DD')}以后`
                    : `活动结束时间前${shopOrderInfo.orderRetentionDays}天内`}
                  的订单数据 ，如需查询更早的历史订单数据，请联系对应客户经理。
                  <br />
                  2、若希望预售消费者参与本次活动，请在设置活动下单时间时包含预售开始时间（注：预售消费者支付定金的时间即为下单时间）。
                </div>
              </FormItem>
              <FormItem
                label="订单状态"
                extra={
                  <div className="next-form-item-help">
                    已完成：(1)用户订单完成后才可参与活动。(2)预售商品需要支付尾款方可参与活动
                  </div>
                }
              >
                <Select dataSource={ORDER_STATUS} value={formData.orderStatus} onChange={handleStatusChange} />
                <div className={styles.orderTypes}>
                  <span className={`${styles.order} ${formData.orderStatus ? styles.orderGray : ''}`}>已付款</span>
                  <span className={`${styles.order} ${formData.orderStatus ? styles.orderGray : ''}`}>待出库</span>
                  <span className={`${styles.order} ${formData.orderStatus ? styles.orderGray : ''}`}>待发货</span>
                  <span className={styles.order}>已完成</span>
                </div>
              </FormItem>
              <FormItem label="订单笔数" required>
                <FormItem>
                  <Radio.Group
                    value={formData.orderStrokeCount}
                    onChange={(orderStrokeCount) => {
                      handleOederCountChange(orderStrokeCount);
                    }}
                  >
                    <Radio value={1}>单笔</Radio>
                    <Radio value={2}>多笔</Radio>
                  </Radio.Group>
                </FormItem>
                {formData.orderStrokeCount === 2 && (
                  <FormItem required requiredMessage="请输入订单笔数">
                    大于等于{' '}
                    <NumberPicker
                      name="orderStrokeNum"
                      min={1}
                      max={5}
                      type="inline"
                      value={formData.orderStrokeNum}
                      onChange={(orderStrokeNum: number) => setData({ orderStrokeNum })}
                    />{' '}
                    笔
                  </FormItem>
                )}
                <div className={styles.tip}>
                  （1）如设置【单笔】（任意一笔订单）则用户在满足订单规则前提下完成多笔订单，如任意一笔订单金额满足订单金额，则有领奖资格；
                  <br />
                  （2）如设置【多笔】订单，则用户的【符合条件订单数】满足最小订单条件，即可领奖
                </div>
              </FormItem>
              <FormItem label="价格类型">
                <Radio checked>京东价</Radio>
              </FormItem>
              <FormItem
                label={formData.orderSkuType ? '指定商品订单总金额' : '总订单金额'}
                required
                requiredMessage="请输入订单金额"
              >
                大于等于{' '}
                <NumberPicker
                  precision={2}
                  name="orderPriceLimit"
                  min={1}
                  max={9999999}
                  type="inline"
                  value={formData.orderPriceLimit}
                  onChange={(orderPriceLimit: number) => setData({ orderPriceLimit })}
                />{' '}
                元
              </FormItem>
              <FormItem label="订单商品" required>
                <Radio.Group
                  value={formData.orderSkuType}
                  onChange={(orderSkuType: number) => {
                    formData.orderSkuList = [];
                    formData.orderSkuListPreview = [];
                    setData({ orderSkuType, orderSkuList: formData.orderSkuList });
                  }}
                >
                  <Radio id="0" value={0}>
                    全部商品
                  </Radio>
                  <Radio id="1" value={1}>
                    指定商品
                  </Radio>
                  {/* <Radio id="2" value={2}> */}
                  {/*  排除商品 */}
                  {/* </Radio> */}
                </Radio.Group>
                <Grid.Row>
                  {formData.orderSkuType === 1 && (
                    <FormItem
                      name="orderSkuList"
                      required
                      requiredMessage={'请选择指定商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} />
                      <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={removeSku} />
                      <p className={styles.tip}>注：选择指定商品后，活动也展示被添加商品，上限展示500个</p>
                      <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList} />
                    </FormItem>
                  )}
                  {formData.orderSkuType === 2 && (
                    <FormItem
                      name="orderSkuList"
                      required
                      requiredMessage={'请选择排除商品'}
                      style={{ marginTop: '15px' }}
                    >
                      <ChooseGoods value={formData.orderSkuList} onChange={handleSkuChange} />
                      <SkuList skuList={formData.orderSkuList} handlePreview={handlePreview} removeSku={removeSku} />
                      <p className={styles.tip}>
                        注：选择排除商品后，活动也展示全店商品去掉排除商品后的商品，上限展示500个
                      </p>
                      <Input className="validateInput" name="orderSkuList" value={formData.orderSkuList} />
                    </FormItem>
                  )}
                </Grid.Row>
              </FormItem>
            </>
          )}
        </Form>
      </LzPanel>
    </div>
  );
};
