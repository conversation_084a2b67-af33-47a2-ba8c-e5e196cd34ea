/**
 * Author: <PERSON><PERSON><PERSON><PERSON>
 * Date: 2023-05-30 10:53
 * Description:
 */
import React, { useEffect, useRef, useState } from 'react';
import styles from './style.module.scss';
import LzSteps from '@/components/LzSteps';
// 设置模版
import Customize from './customize';
// 活动设置
import Setting from './setting';
// 活动预览
import Preview from './preview';
// 活动完成
import Complete from './complete';
import QRCode from 'qrcode.react';
import LzPanel from '@/components/LzPanel';
// 风险控制组件
import LzRiskConfirm from '@/components/LzRiskConfirm';
import { Button, Dialog, Form, Input, Loading, Message } from '@alifd/next';
import { config } from 'ice';
import {
  checkActivityData,
  CustomValue,
  DEFAULT_CUSTOM_VALUE,
  INIT_PAGE_DATA,
  PageData,
  QRCODE_SETTING,
  QRCODE_SIZE,
  STEPS,
} from './util';
import { tmpSaveActivityData } from '@/api/preview';
import { addActivityTemplate, getDefinedActivityTemplateCount } from '@/api/template';
import { getActivityInfoAllOrigin } from '@/api/common';
import { createDraft } from '@/api/draft';
import { createActivity, updateActivity } from '@/api/v99101';
import { uploadImage } from '@/api/image';
import { deepCopy, getActivityParams, getJdAppPreviewUrl, getParams } from '@/utils';
import { getShop } from '@/utils/shopUtil';
import LzPhoneHeader from '@/components/LzPhoneHeader';

export default (props) => {
  const { history } = props;
  const [loading, setLoading] = useState<boolean>(false);
  const [riskLoading, setRiskLoading] = useState<boolean>(false);
  const [dialogLoading, setDialogLoading] = useState<boolean>(false);
  // 当前装修区域
  // 来源自iframe
  const [target, setTarget] = useState<number>(1);
  // 步骤
  const [step, setStep] = useState<number>(1);
  // 自定义模板数据
  const [templateCount, setTemplateCount] = useState<number | undefined>(0);
  // 装修后的数据
  const [customInfo, setCustomInfo] = useState<Required<CustomValue> | undefined>();
  // 装修默认数据
  const [defaultCustomInfo, setDefaultCustomInfo] = useState<Required<CustomValue>>(deepCopy(DEFAULT_CUSTOM_VALUE));
  // 风险控制dialog
  const [risk, setRisk] = useState<boolean>(false);
  // 保存模板dialog
  const [templateTitleDialog, setTemplateTitleDialog] = useState<boolean>(false);
  // 活动设置数据
  const [activityInfo, setActivityInfo] = useState<Required<PageData> | undefined>();
  // 活动默认数据
  const [defaultActivityInfo, setDefaultActivityInfo] = useState<Required<PageData>>(deepCopy(INIT_PAGE_DATA()));
  // iframe DOM
  const iframeRef = useRef<HTMLIFrameElement | null>(null);
  // 活动设置模块Ref 用于校验各组件必填
  const settingRef = useRef<{ submit: () => void | null }>(null);
  // 模板标题
  const [title, setTitle] = useState<string>('');
  // 模板图片blob流
  const [screen, setScreen] = useState<Blob>();
  // 获取活动类型 & 模板ID
  const [activityType, code] = getActivityParams();
  // 预览id
  const [tmpId, setTmpId] = useState<string>('');
  // 创建好的活动id
  const [activityId, setActivityId] = useState<string>('');
  // 当前操作类型
  const operationType: string = getParams('type');

  /**
   * 向c端发送消息
   */
  const sendMessage = (formData, type): void => {
    const iWindow = iframeRef.current?.contentWindow;
    // console.log(`📧📧📧向c端发送消息  ${new Date()}`, type);
    // console.log(formData);
    iWindow?.postMessage(
      {
        from: 'B',
        type,
        event: 'update',
        data: formData,
      },
      '*',
    );
  };
  /**
   * 通知C端截图
   */
  const getScreen = (): void => {
    setLoading(true);
    sendMessage({}, 'screen');
  };
  /**
   * 处理接收到截图信息
   * 存入图片空间
   * 另存自定义模板
   */
  const saveCustomerTemplate = async (): Promise<void> => {
    if (!title) {
      Message.error('请填写模板名称');
      return;
    }
    setDialogLoading(true);
    if (screen) {
      const file = new File([screen], `screen${Date.now()}`, {
        type: screen.type,
        lastModified: Date.now(),
      });
      const form: FormData = new FormData();
      form.append('file', file);
      const { pictureUrl } = await uploadImage(
        {
          pictureCategoryId: '132790541',
        },
        form,
      );
      await addActivityTemplate({
        activityType,
        code,
        configValue: JSON.stringify(customInfo || defaultCustomInfo),
        cover: `//img10.360buyimg.com/imgzone/${pictureUrl}`,
        title,
        username: 'test',
      });
      setDialogLoading(false);
      Message.success('另存为自定义模板成功');
      setTemplateTitleDialog(false);
      setTitle('');
      await getCustomerTemplateCount();
    }
  };
  /**
   * 另存为自定义模板confirm
   */
  const saveCustomerTemplateConfirm = (data): void => {
    setLoading(false);
    setScreen(data);
    Dialog.confirm({
      v2: true,
      title: '提示',
      centered: true,
      content: '保存为自定义模板可方便您日后重复使用，是否保存在自定义模板?',
      onOk: async (): Promise<void> => {
        setTemplateTitleDialog(true);
      },
    } as any);
  };
  /**
   * 保存草稿箱
   */
  const saveDraft = async (): Promise<void> => {
    await createDraft({
      activityType,
      code,
      decoData: JSON.stringify(customInfo || defaultCustomInfo),
      activityData: JSON.stringify(activityInfo || defaultActivityInfo),
    });
    Message.success('保存草稿成功！');
  };

  const getCustomerTemplateCount = async (): Promise<void> => {
    const { count } = await getDefinedActivityTemplateCount({ activityType: activityType.toString() });
    setTemplateCount(count);
  };

  /**
   * 初始化获取活动数据, 发送数据
   */
  const fetchActivityData = (): void => {
    setLoading(true);
    getActivityInfoAllOrigin({ id: getParams('id'), type: operationType }).then((res) => {
      if (iframeRef.current) {
        const customData = JSON.parse(res.decoData as string);
        if (res.decoData) {
          setDefaultCustomInfo(customData);
          // 发送装修数据
          sendMessage(customData, 'deco');
          // 发送选中
          sendMessage(true, 'border');
        }
        if (res.activityData) {
          const activityData = JSON.parse(res.activityData as string);
          // 复制时，清空奖品列表&活动规则
          if (operationType === 'copy') {
            activityData.seriesList = [];
            activityData.seriesPrizeList = [];
            activityData.fileList = [];
            activityData.fileName = '';
            activityData.rules = '';
          }
          if (operationType !== 'tpl') {
            customData!.cmdImg = activityData.cmdImg || customData!.cmdImg;
            customData!.h5Img = activityData.h5Img || customData!.h5Img;
            customData!.mpImg = activityData.mpImg || customData!.mpImg;
            setDefaultCustomInfo(customData!);
          }
          setDefaultActivityInfo(deepCopy(activityData));
          setActivityInfo(activityData);
          // 发送活动设置数据
          sendMessage(activityData, 'activity');
        }
        const { shopName } = getShop();
        sendMessage(shopName, 'shop');
      }
      setLoading(false);
    });
  };
  /**
   * 接收到来自c的消息
   */
  const receiveMessage = (res): void => {
    if (res.data.type === 'deco') {
      // 点击对应的装修区块
      const { data } = res.data;
      setTarget(data);
    } else if (res.data.type === 'screen') {
      // 获取截图
      console.log('收到截图信息');
      const { data } = res.data;
      saveCustomerTemplateConfirm(data);
    } else if (res.data.type === 'mounted') {
      console.log('c端加载完成');
      fetchActivityData();
    }
  };
  /**
   * 自定义模板页数据改变
   * @param formData 装修数据
   */
  const handleCustomizeChange = (formData): void => {
    setCustomInfo(formData);
    sendMessage(formData, 'deco');
  };
  /**
   * 活动设置内容
   * @param data 活动配置数据
   * @param type 用于c端弹出任务列表
   */
  const handleSettingChange = (data, type?: string): void => {
    console.log(data, '活动数据更新');
    setActivityInfo(data);
    sendMessage(data, 'activity');
    sendMessage(type === 'task', 'task');
  };
  /**
   * 设置成功的回调
   * @param errors 活动设置各Form校验后返回的异常
   */
  const handleSubmitSetting = (errors): void => {
    if (!errors) {
      const isValidateData: boolean = checkActivityData(activityInfo!);
      if (!isValidateData) {
        return;
      }
      console.log('保存预览数据', activityInfo);
      tmpSaveActivityData({
        // 接口定的VO有问题
        activityData: { ...(activityInfo || defaultActivityInfo), templateCode: code } as any,
        decoData: JSON.stringify(customInfo || defaultCustomInfo),
      }).then((res): void => {
        setTmpId(res.activityId!);
        setStep(step + 1);
      });
    }
  };
  /**
   * 风险提示确认回调
   */
  const handleRiskConfirm = (): void => {
    setRiskLoading(true);
    const isEdit: boolean = operationType === 'edit';
    const events: [Function, Function] = [createActivity, updateActivity];
    events[+isEdit]({
      activityData: { ...(activityInfo || defaultActivityInfo), templateCode: code, activityId: getParams('id') },
      decoData: JSON.stringify(customInfo || defaultCustomInfo),
    })
      .then((res: { activityId: string }): void => {
        setActivityId(res.activityId);
        setRisk(false);
        setRiskLoading(false);
        Message.success(`活动${isEdit ? '编辑' : '创建'}成功`);
        setStep(step + 1);
      })
      .catch((err) => {
        setRiskLoading(false);
        Message.error(err.message);
      });
  };
  useEffect(() => {
    // 获取自定义模板数量
    getCustomerTemplateCount().then((): void => {});
  }, []);
  // 初始化监听/销毁postMessage信息
  useEffect(() => {
    // 当监听事件中存在对state值的访问时，无法获取监听后的值，需要重新绑定事件
    window.addEventListener('message', receiveMessage, false);
    return (): void => {
      window.removeEventListener('message', receiveMessage);
    };
    // 改变后重新绑定事件
  }, [customInfo]);
  // 监听步骤条，开启选中态
  useEffect((): void => {
    if (step === 1) {
      sendMessage(true, 'border');
    }
  }, [step]);
  // 设置模板按钮
  const StepOneBtn = () => (
    <div className="crm-footer">
      <Button onClick={getScreen} disabled={templateCount! >= 20}>
        另存自定义模板({templateCount}/20)
      </Button>
      <Button onClick={saveDraft}>保存草稿箱</Button>
      {operationType !== 'edit' && (
        <Button onClick={() => history.push(`/activity/select?activityType=${activityType}`)}>重新选择模板</Button>
      )}
      <Button
        type="primary"
        onClick={() => {
          setStep(step + 1);
          sendMessage(false, 'border');
          sendMessage('index', 'popup');
        }}
        disabled={loading}
      >
        下一步
      </Button>
    </div>
  );
  // 活动设置按钮
  const StepTwoBtn = () => (
    <div className="crm-footer">
      <Button onClick={saveDraft}>保存草稿箱</Button>
      <Button onClick={() => setStep(step - 1)}>上一步</Button>
      <Button
        type="primary"
        onClick={() => {
          settingRef.current!.submit();
        }}
      >
        下一步
      </Button>
    </div>
  );
  // 活动预览按钮
  const StepThreeBtn = () => (
    <div className="crm-footer">
      <Button onClick={() => setStep(step - 1)}>上一步</Button>
      <Button type="primary" onClick={() => setRisk(true)}>
        完成
      </Button>
    </div>
  );
  // 渲染步骤按钮
  const renderStep = () => {
    const stepMap = {
      1: <StepOneBtn />,
      2: <StepTwoBtn />,
      3: <StepThreeBtn />,
    };
    return stepMap[step];
  };
  return (
    <div className="crm-container">
      <Loading visible={loading} inline={false}>
        <LzSteps steps={STEPS} current={step} />
        <div className={styles.container}>
          <div
            className={styles.preview}
            style={{
              marginTop: 0,
              height: step === 3 ? 640 : 540,
            }}
          >
            <div className={styles.phone}>
              <LzPhoneHeader step={step} value={activityInfo || defaultActivityInfo} />
              <iframe
                ref={iframeRef}
                height={1502}
                width={750}
                src={`${config.previewUrl}/${activityType}/${code}/preview/?id=${getParams('id')}`}
                style={{ borderRadius: '0 0 50px 50px' }}
                frameBorder="0"
              />
            </div>
            {step === 3 && (
              <div className={styles.qrcode}>
                <QRCode
                  size={QRCODE_SIZE}
                  value={getJdAppPreviewUrl(`/${activityType}/${code}/preview/?id=${tmpId}&type=preview`)}
                  imageSettings={QRCODE_SETTING}
                />
                <span>京东APP扫码预览</span>
              </div>
            )}
          </div>

          <div className={styles.operation}>
            {step === 1 && (
              <Customize
                target={target}
                defaultValue={defaultCustomInfo}
                value={customInfo}
                handleChange={handleCustomizeChange}
                handleChangeActiveKey={(activeKey) => sendMessage(activeKey, 'popup')}
              />
            )}
            {step === 2 && (
              <Setting
                sRef={settingRef}
                onSettingChange={handleSettingChange}
                defaultValue={defaultActivityInfo}
                value={activityInfo}
                decoValue={defaultCustomInfo}
                onSubmit={handleSubmitSetting}
              />
            )}
            {step === 3 && (
              <LzPanel title="活动预览">
                <Preview defaultValue={defaultActivityInfo} value={activityInfo} />
              </LzPanel>
            )}
            {step === 4 && <Complete history={history} activityId={activityId} />}
          </div>
        </div>
        {renderStep()}
      </Loading>
      <LzRiskConfirm
        loading={riskLoading}
        riskVisible={risk}
        onClose={() => setRisk(false)}
        onSubmit={handleRiskConfirm}
      />
      <Dialog
        v2
        title="保存模板"
        centered
        visible={templateTitleDialog}
        okProps={{ loading: dialogLoading }}
        onOk={saveCustomerTemplate}
        onClose={() => {
          setTemplateTitleDialog(false);
          setTitle('');
        }}
      >
        <Form>
          <Form.Item label="模板名称" required>
            <Input placeholder="请填写模板名称" value={title} onChange={(value) => setTitle(value)} maxLength={20} />
          </Form.Item>
        </Form>
      </Dialog>
    </div>
  );
};
