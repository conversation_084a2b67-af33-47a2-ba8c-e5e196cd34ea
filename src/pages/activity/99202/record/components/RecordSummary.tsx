import React, { useEffect, useReducer, useState } from 'react';
import { Button, DatePicker2, Field, Form, Input, Table } from '@alifd/next';
import LzPagination from '@/components/LzPagination';
import { dataPrizeData, dataPrizeDataExport } from '@/api/v99202';
import { deepCopy, downloadExcel } from '@/utils';
import LzPanel from '@/components/LzPanel';
import format from '@/utils/format';

import dayjs from 'dayjs';

const FormItem = Form.Item;
const { RangePicker } = DatePicker2;

const defaultPage = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};

interface FormLayout {
  labelCol: {
    span?: number;
    fixedSpan?: number;
  };
  wrapperCol: {
    span: number;
  };
  labelAlign: 'left' | 'top';
  colon: boolean;
}

export const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};
export default (props) => {
  const field = Field.useField();
  const [pageInfo, setPage] = useState(deepCopy(defaultPage));
  const [tableHeaderData, setTableHeaderData] = useState<any[]>([]);
  const [tableData, setTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const handleSubmit = (e) => {
    e.preventDefault();
    const searchtData = {
      startDate: params.startTime || '',
      endDate: params.endTime || '',
      addParams: {
        actName: actName || '',
        pin: pinInfo || '',
      },
    };
    loadData(searchtData, defaultPage);
  };

  const loadData = (data, page): void => {
    setLoading(true);
    dataPrizeData({ ...data, ...page })
      .then((res: any): void => {
        for (let i = 0; i < res.data.length; i++) {
          res.data[i].num = i + 1;
        }
        setTableData(res.data as any[]);
        setTableHeaderData(res.tableHeader as any[]);
        pageInfo.total = +res.total!;
        pageInfo.pageSize = +res.pageSize!;
        pageInfo.pageNum = +res.pageNum!;
        setPage(pageInfo);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const reSetloadData = () => {
    setPinInfo('');
    setActName('');
    setParams({
      startTime: '',
      endTime: '',
    });
    setPage(defaultPage);
    const resetData = {
      startDate: '',
      endDate: '',
      addParams: {
        actName: '',
        pin: '',
      },
    };
    loadData(resetData, defaultPage);
  };
  const handlePage = ({ pageSize, pageNum }) => {
    const handleData = {
      startDate: params.startTime || '',
      endDate: params.endTime || '',
      addParams: {
        actName: actName || '',
        pin: pinInfo || '',
      },
    };
    loadData(handleData, { pageSize, pageNum });
  };

  const exportData = () => {
    const d = {
      startDate: params.startTime || '',
      endDate: params.endTime || '',
      addParams: {
        actName: actName || '',
        pin: pinInfo || '',
      },
    };
    dataPrizeDataExport({ ...d, ...defaultPage }).then((data: any) =>
      downloadExcel(data, `${dayjs(new Date()).format('YYYY-MM-DD')}-购物赠送领取记录`),
    );
  };

  useEffect(() => {
    const formValue: any = field.getValues();
    console.log(formValue);
    const initData = {
      startDate: params.startTime || '',
      endDate: params.endTime || '',
      addParams: {
        actName: actName || '',
        pin: pinInfo || '',
      },
    };
    loadData(initData, { ...defaultPage });
  }, []);
  const [pinInfo, setPinInfo] = useState('');
  // 获取输入的pin 信息
  const getPinInfo = (value) => {
    setPinInfo(value);
  };
  const [actName, setActName] = useState('');
  // 输入活动名称
  const searchActName = (value) => {
    setActName(value);
  };
  const [params, setParams] = useReducer((prevState, currState) => ({ ...prevState, ...currState }), {
    startTime: '',
    endTime: '',
  });
  // 领取记录日期改变，处理提交数据
  const onDataRangeChange = (rangeDate): void => {
    setParams({
      startTime: format.formatDateTimeDayjs(rangeDate[0]),
      endTime: format.formatDateTimeDayjs(rangeDate[1]),
    });
  };

  return (
    <LzPanel>
      <div style={{ minHeight: '350px' }}>
        <Form
          className="lz-query-criteria"
          style={{ marginBottom: '20px' }}
          field={field}
          {...formItemLayout}
          onSubmit={handleSubmit}
        >
          <FormItem name="userPin" label="用户ID" requiredMessage="请输入用户ID">
            <Input trim onChange={getPinInfo} placeholder="请输入用户ID" />
          </FormItem>
          <FormItem name="searchActName" label="活动名称" requiredMessage="请输入活动名称">
            <Input trim maxLength={10} onChange={searchActName} placeholder="请输入活动名称" />
          </FormItem>
          <FormItem name="dateRange2" label="领取时间">
            <RangePicker
              format="YYYY-MM-DD HH:mm:ss"
              showTime
              timePanelProps={{
                defaultValue: ['00:00:00', '23:59:59'],
                format: 'HH:mm:ss',
              }}
              onChange={onDataRangeChange}
            />
          </FormItem>
          <FormItem colon={false}>
            <Form.Submit type="primary" htmlType="submit">
              查询
            </Form.Submit>
            <Form.Reset toDefault onClick={() => reSetloadData()}>
              重置
            </Form.Reset>
            <Button onClick={exportData} type="primary">
              导出
            </Button>
          </FormItem>
        </Form>

        <Table dataSource={tableData} loading={loading}>
          <Table.Column width={60} title="序号" dataIndex="num" />
          {tableHeaderData.map((item, index) => (
            <Table.Column
              key={index}
              title={item}
              dataIndex={item}
              width={item === '加密pin' ? 550 : 200}
              cell={(value, index, record) => (
                <span>
                  {!item.includes('时间') && value}
                  {item.includes('时间') && dayjs(value).format('YYYY-MM-DD HH:mm:ss')}
                </span>
              )}
            />
          ))}
        </Table>
        <LzPagination
          pageNum={pageInfo.pageNum}
          pageSize={pageInfo.pageSize}
          total={pageInfo.total}
          onChange={handlePage}
        />
      </div>
    </LzPanel>
  );
};
