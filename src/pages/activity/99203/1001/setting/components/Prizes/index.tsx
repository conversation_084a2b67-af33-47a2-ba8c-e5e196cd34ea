/**
 * Author: z<PERSON><PERSON><PERSON>
 * Date: 2023-06-08 14:58
 * Description:
 */
import React, { useEffect, useImperativeHandle, useReducer, useState } from 'react';
import { Button, Dialog, Field, Form, NumberPicker, Table } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import LzDialog from '@/components/LzDialog';
import ChoosePrize from '../Dialog/ChoosePrize';
import { activityEditDisabled, deepCopy } from '@/utils';
import { FormLayout, generateRandomString, PageData, PRIZE_INFO, PRIZE_TYPE, PrizeInfo } from '../../../util';
import styles from '../../style.module.scss';

interface Props {
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void }>;
  onChange: (formData: Required<PageData>) => void;
}

const FormItem = Form.Item;
const formItemLayout: FormLayout = {
  labelCol: {
    span: 4,
  },
  wrapperCol: {
    span: 20,
  },
  labelAlign: 'left',
  colon: true,
};

export default ({ sRef, onChange, defaultValue, value }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);
  const field: Field = Field.useField();
  const addField: Field = Field.useField();

  // 选择奖品
  const [visible, setVisible] = useState(false);
  // 编辑情况下追加库存弹窗
  const [addStockVisible, setAddStockVisible] = useState(false);
  const [addStockNumber, setAddStockNumber] = useState(0);
  // 当前编辑行数据
  const [editValue, setEditValue] = useState(null);
  // 当前编辑行index
  const [target, setTarget] = useState(0);
  // 当前编辑的表格
  const [tableName, setTableName] = useState('');
  // 暂存累计签到天数
  const [signDaySto, setSignDaySto] = useState('');

  // 同步/更新数据
  const setData = (data): void => {
    setFormData(data);
    onChange({ ...formData, ...data });
  };

  /**
   * 新建/编辑奖品回调
   * @param data 当前编辑奖品信息
   */
  const onPrizeChange = (data): boolean | void => {
    // 更新指定index 奖品信息
    if (tableName === 'prizeDay') {
      formData.prizeDay[target] = data;
    } else if (tableName === 'prizeList') {
      formData.prizeList[target] = { ...data, signDay: signDaySto, itemId: target + generateRandomString() };
    }
    setData(formData);
    setVisible(false);
  };
  useEffect((): void => {
    setFormData(value || defaultValue);
  }, [value]);

  useEffect((): void => {
    // 初始奖品列表长度
    const prizeListLength = formData.prizeList.length;
    // 生成默认奖品列表
    const list: PrizeInfo[] = [...formData.prizeList];
    // 补齐奖品数到8
    for (let i = 0; i < 1 - prizeListLength; i++) {
      list.push(deepCopy(PRIZE_INFO));
    }
    // 判断是否是编辑活动状态（存在每日奖品）
    const isEditPrizeDay = formData.prizeDay.length > 0;
    // 如果奖品列表为空 说明：奖品列表已经够8 直接用prizeList
    // 如果不为空 说明：奖品列表已经不够8 使用补齐后的list
    setData({
      prizeList: list.length ? list : formData.prizeList,
      prizeDay: isEditPrizeDay ? formData.prizeDay : [deepCopy(PRIZE_INFO)],
      dayStatus: 1,
    });
  }, []);

  useImperativeHandle(sRef, (): { submit: () => null } => ({
    submit: () => {
      let err: object | null = null;
      field.validate((errors): void => {
        err = errors;
      });
      return err;
    },
  }));
  const onCancel = (): void => {
    setVisible(false);
  };

  return (
    <div>
      <LzPanel title="签到奖品设置">
        <Form {...formItemLayout} field={field} disabled={activityEditDisabled()}>
          <FormItem required label="每日签到奖励">
            <FormItem>
              <Table dataSource={formData.prizeDay} style={{ marginTop: '15px' }}>
                <Table.Column title="奖品名称" dataIndex="prizeName" />
                <Table.Column
                  title="奖品类型"
                  cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                  dataIndex="prizeType"
                />
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    if (row.prizeType === 1) {
                      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                    } else {
                      return <div>{row.unitCount ? `${row.unitCount}份` : ''}</div>;
                    }
                  }}
                />
                <Table.Column
                  title="发放份数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
                {!activityEditDisabled() && (
                  <Table.Column
                    title="操作"
                    width={130}
                    cell={(val, index, _) => (
                      <FormItem>
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            let row = formData.prizeDay[index];
                            if (row.prizeName === '') {
                              row = null;
                            }
                            setEditValue(row);
                            setTarget(index);
                            setTableName('prizeDay');
                            setVisible(true);
                          }}
                        >
                          <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                        </Button>
                      </FormItem>
                    )}
                  />
                )}
              </Table>
            </FormItem>
          </FormItem>
          <FormItem required label="累计奖品设置">
            {!activityEditDisabled() && (
              <FormItem>
                <Button
                  disabled={formData.prizeList.length >= 10}
                  type="primary"
                  onClick={() => {
                    formData.prizeList.push(deepCopy(PRIZE_INFO));
                    setData(formData);
                  }}
                >
                  +添加奖品（{formData.prizeList.length}/10）
                </Button>
              </FormItem>
            )}
            <FormItem>
              <Table dataSource={formData.prizeList} style={{ marginTop: '15px' }}>
                <Table.Column
                  title={'累计签到天数'}
                  cell={(_, index, row) => (
                    <FormItem required requiredMessage="请输入签到天数">
                      <NumberPicker
                        min={1}
                        max={9999999}
                        step={1}
                        type="inline"
                        value={row.signDay}
                        onChange={(signDay: number) => {
                          formData.prizeList[index].signDay = signDay;
                          setData(formData);
                        }}
                        name={`signDay-${index}`}
                      />
                    </FormItem>
                  )}
                />
                <Table.Column title="奖品名称" dataIndex="prizeName" />
                <Table.Column
                  title="奖品类型"
                  cell={(_, index, row) => <div>{PRIZE_TYPE[row.prizeType]}</div>}
                  dataIndex="prizeType"
                />
                <Table.Column
                  title="单位数量"
                  cell={(_, index, row) => {
                    if (row.prizeType === 1) {
                      return <div>{row.numPerSending ? `${row.numPerSending}张` : ''}</div>;
                    } else {
                      return <div>{row.unitCount ? `${row.unitCount}份` : '1份'}</div>;
                    }
                  }}
                />
                <Table.Column
                  title="发放份数"
                  cell={(_, index, row) => <div>{row.sendTotalCount ? `${row.sendTotalCount}份` : ''}</div>}
                />
                <Table.Column
                  title="奖品图"
                  cell={(_, index, row) => <img style={{ width: '30px' }} src={row.prizeImg} alt="" />}
                />
                <Table.Column
                  title="操作"
                  width={130}
                  cell={(val, index, _) => (
                    <div>
                      {!activityEditDisabled() && (
                        <>
                          <Button
                            text
                            type="primary"
                            onClick={() => {
                              setSignDaySto(formData.prizeList[index].signDay);
                              let row = formData.prizeList[index];
                              if (row.prizeName === '') {
                                row = null;
                              }
                              setEditValue(row);
                              setTarget(index);
                              setTableName('prizeList');
                              setVisible(true);
                            }}
                          >
                            <i className={`iconfont icon-chaojiyingxiaoicon-25`} />
                          </Button>

                          {formData.prizeList.length > 1 && (
                            <Button
                              text
                              type="primary"
                              onClick={() => {
                                Dialog.confirm({
                                  v2: true,
                                  title: '提示',
                                  centered: true,
                                  content: '确认删除该奖品？',
                                  onOk: () => {
                                    formData.prizeList.splice(index, 1);
                                    setData(formData);
                                  },
                                  onCancel: () => console.log('cancel'),
                                } as any);
                              }}
                            >
                              <i className={`iconfont icon-shanchu`} />
                            </Button>
                          )}
                        </>
                      )}

                      {activityEditDisabled() && _.prizeType !== 1 && (
                        <Button
                          text
                          type="primary"
                          onClick={() => {
                            const row = formData.prizeList[index];
                            console.log(row);
                            addField.setValues({
                              ...row,
                            });
                            setEditValue(row);
                            setTarget(index);
                            setAddStockVisible(true);
                          }}
                        >
                          追加
                        </Button>
                      )}
                    </div>
                  )}
                />
              </Table>
              <div className={styles.tip}>
                参与次数：达到累计签到天数并领取奖品后，签到记录不清零。如：累计签到5天并领取奖品，则第6天的累计签到记录是6天。
              </div>
            </FormItem>
          </FormItem>
        </Form>
      </LzPanel>

      <LzDialog
        title={'追加奖品'}
        visible={addStockVisible}
        footer={false}
        afterClose={() => setAddStockNumber(0)}
        onClose={() => {
          setAddStockVisible(false);
        }}
        style={{ width: '670px', height: '280px' }}
      >
        {formData?.prizeList[target] && (
          <Form {...formItemLayout} field={addField}>
            <FormItem label="奖品名称">
              <p>{formData.prizeList[target].prizeName}</p>
            </FormItem>
            <FormItem required label="本次追加数量" requiredMessage="请输入本次追加数量">
              <NumberPicker
                min={0}
                max={
                  addField.getValue('prizeType') === 1 || addField.getValue('prizeType') === 3
                    ? Number(addField.getValue('quantityRemain')) - Number(addField.getValue('sendTotalCount'))
                    : 9999
                }
                step={1}
                type="inline"
                value={addStockNumber}
                onChange={(val: number) => {
                  setAddStockNumber(val);
                }}
              />
              {(addField.getValue('prizeType') === 1 || addField.getValue('prizeType') === 3) && (
                <p style={{ color: 'red' }}>
                  最大可追加数量:{' '}
                  {Number(addField.getValue('quantityRemain')) - Number(addField.getValue('sendTotalCount'))}
                </p>
              )}
            </FormItem>
            <FormItem label="追加后数量">
              <p>{addStockNumber + Number(addField.getValue('sendTotalCount'))}</p>
            </FormItem>

            <FormItem colon={false} style={{ marginTop: '20px' }}>
              <Form.Submit
                type="primary"
                disabled={addStockNumber <= 0}
                onClick={() => {
                  setAddStockVisible(false);
                  formData.prizeList[target].sendTotalCount += addStockNumber;
                  setFormData(deepCopy(formData));
                  setData(deepCopy(formData));
                }}
                style={{ marginLeft: '200px' }}
              >
                保存
              </Form.Submit>
              <Button onClick={() => setAddStockVisible(false)}>取消</Button>
            </FormItem>
          </Form>
        )}
      </LzDialog>

      <LzDialog
        title={false}
        visible={visible}
        footer={false}
        onClose={() => setVisible(false)}
        style={{ width: '670px', height: '620px' }}
      >
        <ChoosePrize
          formData={formData}
          typeList={tableName === 'prizeDay' ? [4] : [1, 3, 4, 901, 902]}
          editValue={editValue}
          onChange={onPrizeChange}
          onCancel={onCancel}
          hasProbability={false}
          hasLimit={false}
        />
      </LzDialog>
    </div>
  );
};
