import React, { useEffect, useState } from 'react';
import { Button, Field, Table } from '@alifd/next';
import { dataDailyDataTTL, dataDailyDataTTLExport } from '@/api/v99210';
import { downloadExcel, getParams } from '@/utils';
import LzPanel from '@/components/LzPanel';

export default (props) => {
  const field = Field.useField();
  const [ttlTableData, setTtlTableData] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);

  const loadData = (query: any): void => {
    setLoading(true);
    query.activityId = getParams('id');
    dataDailyDataTTL(query)
      .then((res: any): void => {
        const data: any = [];
        data.push(res);
        setTtlTableData(data);
        setLoading(false);
      })
      .catch((e) => {
        setLoading(false);
      });
  };
  const exportData = () => {
    const formValue: any = field.getValues();
    formValue.activityId = getParams('id');
    dataDailyDataTTLExport(formValue).then((data: any) => downloadExcel(data, 'TTL数据'));
  };
  useEffect(() => {
    const formValue: any = field.getValues();
    loadData({ ...formValue });
  }, []);

  return (
    <LzPanel>
      <div style={{ margin: '10px 0', textAlign: 'right' }}>
        <Button onClick={exportData}>导出</Button>
      </div>
      <Table dataSource={ttlTableData} loading={loading}>
        <Table.Column title="PV" dataIndex="pv" />
        <Table.Column title="UV" dataIndex="uv" />
        <Table.Column title="入会人数" dataIndex="memberNum" />
        <Table.Column title="领取令牌数" dataIndex="receiveNum" />
        <Table.Column title="领取-付款人数" dataIndex="buyNum" />
        <Table.Column title="领取-付款金额" dataIndex="buyPrice" />
        <Table.Column title="UV-付款人数" dataIndex="uvBuyNum" />
        <Table.Column title="UV-付款金额" dataIndex="uvBuyPrice" />
      </Table>
    </LzPanel>
  );
};
