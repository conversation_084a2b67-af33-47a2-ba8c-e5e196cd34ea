import React, { useImperative<PERSON>andle, useReducer, useRef } from 'react';
import { CustomValue, PageData } from '../util';
import BaseInfo from './components/Base';
import Crowd from './components/Crowd';
import ModelList from './components/ModelList';
import PrizeList from './components/PrizeList';
import ProduceList from './components/ProduceList';
import WindowLink from './components/WindowLink';
import styles from './style.module.scss';

interface Props {
  onSettingChange: (activityInfo: PageData, type) => void;
  defaultValue: Required<PageData>;
  value: PageData | undefined;
  sRef: React.Ref<{ submit: () => void; clearNewlyAddedFlags: () => void }>;
  decoValue: CustomValue | undefined;
  onSubmit: (resultListLength: number) => void;
}

interface SettingProps extends Pick<Props, 'defaultValue' | 'value'> {
  onChange: (formData: Required<PageData>) => void;
}

export default ({ onSettingChange, defaultValue, value, sRef, onSubmit, decoValue }: Props) => {
  const [formData, setFormData] = useReducer((p, c) => {
    return { ...p, ...c };
  }, value || defaultValue);

  const onChange = (activityInfo, type?: string): void => {
    setFormData(activityInfo);
    onSettingChange(activityInfo, type);
  };
  const settingProps: SettingProps = {
    onChange,
    defaultValue,
    value,
  };
  // 各Form Ref
  const baseRef = useRef<{ submit: () => void | null }>(null);
  const crowdRef = useRef<{ submit: () => void | null }>(null);
  const shareRef = useRef<{ submit: () => void | null }>(null);
  const sectionSettingRef = useRef<{ submit: () => void | null }>(null);
  const modelListRef = useRef<{ submit: () => void | null }>(null);
  const prizeListRef = useRef<{ submit: () => void | null }>(null);
  const productListRef = useRef<{ submit: () => void | null }>(null);
  const windowLinkRef = useRef<{ submit: () => void | null }>(null);
  const prizeSettingRef = useRef<{ submit: () => void | null; clearNewlyAddedFlags: () => void }>(null);

  // 传递Ref
  useImperativeHandle(sRef, (): { submit: () => void; clearNewlyAddedFlags: () => void } => ({
    submit: (): void => {
      // 异常结果列表
      const resultList: unknown[] = [];
      // 收集所有Form的submit 验证方法
      const events: any[] = [
        baseRef.current,
        crowdRef.current,
        prizeSettingRef.current,
        sectionSettingRef.current,
        shareRef.current,
      ].filter((ref) => ref !== null); // 过滤掉null的ref

      // 批量执行submit方法
      // submit只会抛出异常，未有异常返回null
      events.every((ref, index: number): boolean => {
        console.log('events', events, index);
        // 确保ref存在且有submit方法
        if (ref && typeof ref.submit === 'function') {
          const result = ref.submit();
          console.log('console.log(result);', result);
          if (result) {
            resultList.push(result);
            return false;
          }
        }
        return true;
      });
      onSubmit(resultList.length);
    },
    // 清除新添加奖品的标记
    clearNewlyAddedFlags: (): void => {
      // 调用 Prizes 组件的 clearNewlyAddedFlags 方法
      if (prizeSettingRef.current && typeof prizeSettingRef.current.clearNewlyAddedFlags === 'function') {
        prizeSettingRef.current.clearNewlyAddedFlags();
      }
    },
  }));

  return (
    <div className={styles.setting}>
      <BaseInfo sRef={baseRef} {...settingProps} />
      <Crowd sRef={crowdRef} {...settingProps} />
      <ModelList sRef={modelListRef} {...settingProps} />
      <PrizeList sRef={prizeListRef} {...settingProps} />
      <ProduceList sRef={productListRef} {...settingProps} />
      <WindowLink sRef={windowLinkRef} {...settingProps} />
    </div>
  );
};
