/**
 * 大转盘抽奖数据报表
 */
import React, { useEffect, useState } from 'react';
import { Tab } from '@alifd/next';
import LzPanel from '@/components/LzPanel';
import ActivityDataReport from './components/ActivityDataReport';
import ActivityDataByDayReport from './components/ActivityDataByDayReport';
import LzDocGuide from '@/components/LzDocGuide';

export default () => {
  const [activeKey, setActiveKey] = useState('1');
  return (
    <div className="crm-container">
      <LzPanel title="渠道数据报表" actions={<LzDocGuide />}>
        <Tab defaultActiveKey="1" activeKey={activeKey} onChange={(key) => setActiveKey(key)} unmountInactiveTabs>
          <Tab.Item title="渠道ByDay数据明细" key="1">
            <ActivityDataByDayReport />
          </Tab.Item>
          <Tab.Item title="渠道累计数据报表" key="2">
            <ActivityDataReport />
          </Tab.Item>
        </Tab>
      </LzPanel>
    </div>
  );
};
