import React from 'react';
import styles from './index.module.scss';
import { Slider } from '@alifd/next';

export default ({ data, dispatch, activityActiveTab }) => {
  return (
    <div className={styles.preview}>
      <div className={styles['module-content']}>
        {data.showZhuoRui ? (
          <div className={styles['zr-content']}>
            {/* 卓睿新客 */}
            <div
              className={styles.title}
              style={{
                backgroundImage: `url(${data.titleImg})`,
              }}
            />
            {/* tab切换 */}
            <div className={styles.tabs}>
              <div
                className={`${styles['tab-item']} ${
                  activityActiveTab.newGiftTab === '1' ? `${styles.active}` : `${styles['not-active']}`
                }`}
                onClick={() => {
                  dispatch({ type: 'UPDATE_ACTIVITYACTIVETAB', payload: { biTab: '1', newGiftTab: '1' } });
                }}
              >
                <span className={styles['tab-title']}>{data.tabs.tab1_title}</span>
              </div>
              <div
                className={`${styles['tab-item']} ${
                  activityActiveTab.newGiftTab === '2' ? `${styles.active}` : `${styles['not-active']}`
                }`}
                onClick={() => {
                  dispatch({ type: 'UPDATE_ACTIVITYACTIVETAB', payload: { biTab: '1', newGiftTab: '2' } });
                }}
              >
                <span className={styles['tab-title']}>{data.tabs.tab2_title}</span>
              </div>
            </div>
            <div className={styles['tab-content']}>
              {Object.prototype.hasOwnProperty.call(data, 'tabs') &&
                data.tabs[activityActiveTab.newGiftTab == '1' ? 'tab1' : 'tab2'].map((item, index) => (
                  <div key={index}>
                    <img className={styles.img} key={index} src={item.giftImg} />
                  </div>
                ))}
            </div>
          </div>
        ) : (
          <div className={styles['swiper-content']}>
            {/* 全店新客 */}
            <div
              className={styles.title}
              style={{
                backgroundImage: `url(${data.titleImg})`,
              }}
            />
            <div className={styles.swiper}>
              <Slider slidesToShow={3} arrowPosition="inner" dots={false} arrows={false} autoplay lazyLoad>
                {data.swiperList?.map((item, index) => (
                  <div
                    key={index}
                    className={styles['swiper-img']}
                    style={{ backgroundImage: `url(${item.giftImg})` }}
                  />
                ))}
              </Slider>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};
