.notice {
  display: flex;
  font-size: 12px;
  font-family: PingFang SC;
  font-weight: 500;
  color: #333;
  background: #f0faff;
  border-radius: 5px;
  padding: 10px;
  margin-bottom: 20px;

  .font {
    font-size: 12px;
    font-family: PingFang SC;
    font-weight: 500;
    color: #333;
    line-height: 20px;
  }
}

.mr15 {
  margin-right: 15px;
}

.mr10 {
  margin-right: 10px;
}

.w88 {
  width: 88px;
}

.labelWidth {
  width: 30%;
}

.createButton_Box {
  display: flex;
  justify-content: flex-end;
  align-items: center;
}

.createButton {
  width: 130px;
  margin-bottom: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.confirmDialog {
  margin-bottom: 15px;
  text-align: center;
}

.mask {
  position: fixed;
  inset: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 99;
}
