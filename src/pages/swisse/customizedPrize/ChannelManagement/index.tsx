// Swisse奖品管理 - 奖品维护;
import React, { useEffect, useState } from 'react';
import { Box, Button, DatePicker2, Dialog, Field, Form, Input, Loading, Radio } from '@alifd/next';
import LzMsg from '@/components/LzMsg';
import {
  deliveryPrizeCreateChannel,
  deliveryPrizeGetChannelData,
  deliveryPrizeUpdateChannel,
  deliveryPrizeUpDownChannel,
} from '@/api/swisse';
import LzPanel from '@/components/LzPanel';
import dayjs from 'dayjs';
import type { ColumnConfig } from '../CustomTable';
import CustomTable from '../CustomTable';
import type {
  BdSwisseDeliveryChannel,
  IPageBdSwisseDeliveryChannel,
  SwisseDeliveryChannelCreateOrUpdateRequest,
} from '@/api/types';

const FormItem = Form.Item;

interface Pager {
  pageNum: number;
  pageSize: number;
  total: number;
}

const initPager: Pager = {
  pageNum: 1,
  pageSize: 10,
  total: 0,
};
export default () => {
  const field = Field.useField();
  const [loading, setLoading] = useState(false);
  const [list, setList] = useState<BdSwisseDeliveryChannel[]>([]);
  const [startDate, setStartDate] = useState(dayjs().subtract(30, 'day').format('YYYY-MM-DD'));
  const [endTime, setEndTime] = useState(dayjs().format('YYYY-MM-DD'));
  const [pageInfo, setPageInfo] = useState<Pager>(initPager);
  const [prizeInfoDialog, setPrizeInfoDialog] = useState(false);
  useEffect(() => {
    getDataList();
  }, []);
  const handlePage = ({ pageSize, pageNum }) => {
    getDataList({ pageNum, pageSize, total: 0 });
  };
  const getDataList = async (page: Pager = initPager) => {
    const formValue: any = field.getValues();

    const postData = {
      ...formValue,
      ...page,
      startDate: formValue.dataRange && dayjs(formValue.dataRange[0]).format('YYYY-MM-DD'),
      endDate: formValue.dataRange && dayjs(formValue.dataRange[1]).format('YYYY-MM-DD'),
    };
    try {
      setLoading(true);
      const result = (await deliveryPrizeGetChannelData(postData)) as IPageBdSwisseDeliveryChannel;
      setList(result.records || []);
      setPageInfo({
        ...page,
        total: Number(result.total),
      });
    } catch (e) {
      LzMsg.error(e.message);
    } finally {
      setLoading(false);
    }
  };

  const handleSubmit = (v, e) => {
    if (!e) {
      getDataList();
    }
  };
  const defaultColumns: ColumnConfig[] = [
    {
      title: '渠道名称',
      dataIndex: 'channel',
      align: 'center',
    },
    {
      align: 'center',
      title: '创建时间',
      dataIndex: 'createTime',
      cell: (value) => {
        return value && dayjs(value).format('YYYY-MM-DD HH:mm:ss');
      },
    },
    {
      title: '备注',
      dataIndex: 'memo',
      align: 'center',
    },
    {
      title: '操作',
      align: 'center',
      cell: (value, index, record: BdSwisseDeliveryChannel) => {
        return (
          <div>
            <Button
              type="primary"
              style={{ marginRight: '10px' }}
              onClick={() => {
                addFild.setValues({
                  ...record,
                });
                setIsAddPrize(false);
                setPrizeInfoDialog(true);
              }}
            >
              编辑
            </Button>
            <Button
              type="secondary"
              onClick={() => {
                Dialog.confirm({
                  title: '温馨提示',
                  content: `确认${record.effect ? '下架' : '上架'}该渠道？`,
                  onOk: () => {
                    deliveryPrizeUpDownChannel({
                      channelId: record.id!,
                      effect: record.effect ? 0 : 1, // 1:上架 0:下架
                    })
                      .then(() => {
                        LzMsg.success('操作成功');
                        getDataList();
                      })
                      .catch((e) => {
                        LzMsg.error(e.message);
                      })
                      .finally(() => {
                        setLoading(false);
                      });
                  },
                });
              }}
            >
              {record.effect ? '下架' : '上架'}
            </Button>
          </div>
        );
      },
    },
  ];
  const [columns, setColumns] = useState<ColumnConfig[]>(defaultColumns);
  const customTableConfig = {
    dataSource: list,
    columns,
    pageInfo,
    handlePage: (p: Pager) => {
      const { pageNum, pageSize } = p;
      handlePage({ pageNum, pageSize });
    },
  };
  const addFild = Field.useField();
  const [isAddPrize, setIsAddPrize] = useState(false);
  const addPrize = () => {
    setIsAddPrize(true);
    setPrizeInfoDialog(true);
  };
  //   创建或编辑奖品
  const submitChangePrizeInfo = () => {
    const formValue: BdSwisseDeliveryChannel = addFild.getValues();
    const postData: SwisseDeliveryChannelCreateOrUpdateRequest = {
      ...formValue,
    };
    !isAddPrize && (postData.channelId = formValue.id);
    setLoading(true);
    isAddPrize
      ? deliveryPrizeCreateChannel(postData)
          .then((res) => {
            LzMsg.success('操作成功');
            setPrizeInfoDialog(false);
            getDataList();
          })
          .catch((e) => {
            LzMsg.error(e.message);
          })
          .finally(() => {
            setLoading(false);
          })
      : deliveryPrizeUpdateChannel(postData)
          .then((res) => {
            LzMsg.success('操作成功');
            setPrizeInfoDialog(false);
            getDataList();
          })
          .catch((e) => {
            LzMsg.error(e.message);
          })
          .finally(() => {
            setLoading(false);
          });
  };

  // 活动主页
  return (
    <>
      <Loading visible={loading} fullScreen>
        <LzPanel>
          <Form className="lz-query-criteria" field={field} colon labelAlign={'top'}>
            <FormItem name="channel" label="渠道名称">
              <Input trim placeholder="请输入渠道名称" />
            </FormItem>
            <FormItem name="effect" label="是否上架">
              <Radio.Group>
                <Radio value={1}>上架</Radio>
                <Radio value={0}>下架</Radio>
              </Radio.Group>
            </FormItem>
            <FormItem label="创建时间" name="dataRange">
              <DatePicker2.RangePicker
                hasClear={false}
                style={{ width: '100%' }}
                defaultValue={[dayjs(startDate, 'YYYY-MM-DD'), dayjs(endTime, 'YYYY-MM-DD')]}
              />
            </FormItem>
            <FormItem colon={false}>
              <Form.Reset style={{ marginRight: '10px' }}>重置</Form.Reset>
              <Form.Submit validate type="primary" style={{ marginRight: '10px' }} onClick={handleSubmit}>
                查询
              </Form.Submit>
            </FormItem>
          </Form>

          <LzPanel>
            <Button type="primary" onClick={addPrize}>
              新增渠道
            </Button>
          </LzPanel>
          <LzPanel>
            <CustomTable customTableConfig={customTableConfig} />
          </LzPanel>
        </LzPanel>
      </Loading>

      <Dialog
        v2
        title="创建/编辑奖品"
        width={600}
        visible={prizeInfoDialog}
        footer={false}
        onClose={() => setPrizeInfoDialog(false)}
      >
        <Form field={addFild} labelCol={{ span: 4 }} fullWidth useLabelForErrorMessage labelAlign={'left'}>
          <FormItem name="channel" label="渠道名称" required>
            <Input trim placeholder="请填写渠道名称" disabled={!isAddPrize} maxLength={20} />
          </FormItem>
          <FormItem name="memo" label="备注">
            <Input trim placeholder="请输入备注" maxLength={50} />
          </FormItem>
          <FormItem>
            <Box direction="row" justify="flex-end">
              <Form.Submit validate type="primary" style={{ marginRight: '10px' }} onClick={submitChangePrizeInfo}>
                {isAddPrize ? '创建' : '编辑'}
              </Form.Submit>
              <Form.Reset onClick={() => setPrizeInfoDialog(false)}>取消</Form.Reset>
            </Box>
          </FormItem>
        </Form>
      </Dialog>
    </>
  );
};
