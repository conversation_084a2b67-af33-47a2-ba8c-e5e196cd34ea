.message {
  margin-bottom: 15px;
}

.subTitle {
  font-size: 16px;
  color: rgb(0, 0, 0);
}

.copy {
  margin-left: 10px;
  cursor: pointer;
}

.publishView {
  width: 300px;
  display: flex;
  justify-content: space-between;
}

.imgDialog {
  width: 70vw;
  height: 70vh;
  background-size: contain;
  background-position: center;
  background-repeat: no-repeat;
}

.imgCell {
  width: 100px;
  height: 100px;
  object-fit: cover;
  display: block;
  object-position: top;
}

.previewImg {
  width: 70vw;
  object-fit: cover;
}

.rule {
  white-space: pre-wrap;
  word-break: break-all;
}

.preview {
  :global {
    .next-form-preview .next-form-item-control {
      line-height: 28px;
      color: #333;
      white-space: pre-wrap;
      word-break: break-all;
    }
  }
}

.file {
  cursor: pointer;
  overflow: hidden;
  width: 250px;
  margin: 5px 0;

  &:hover {
    color: #1677ff;
  }

  .text {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;

    &:hover {
      width: max-content;
      animation: marqueeAnimation 5s linear infinite;
    }
  }
}

@keyframes marqueeAnimation {
  0% {
    transform: translateX(0);
  }
  100% {
    transform: translateX(-100%);
  }
}
