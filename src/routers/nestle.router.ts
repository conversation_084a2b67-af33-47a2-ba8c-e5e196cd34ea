import { lazy } from 'ice';

const NestleMemberCenter = lazy(() => import('@/pages/nestle/commonDecorate'));
const NestleDataStatements = lazy(() => import('@/pages/nestle/dataStatements'));
const prizeRecords = lazy(() => import('@/pages/nestle/prizeRecords'));

export default [
  {
    name: '雀巢-会员中心',
    path: '/nestle/commonDecorate',
    exact: true,
    component: NestleMemberCenter,
  },
  {
    name: '雀巢-数据报表',
    path: '/nestle/dataStatements',
    exact: true,
    component: NestleDataStatements,
  },
  {
    name: '雀巢-领奖记录',
    path: '/nestle/prizeRecords',
    exact: true,
    component: prizeRecords,
  },
];
