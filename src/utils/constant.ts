/**
 * 定义常量
 */
export default {
  // 应用身份
  LZ_SSO_PRD: 'LZ_SSO_PRD',
  // 统一授权中心
  LZ_SSO_TOKEN: 'LZ_SSO_TOKEN',
  // token在localStorage中缓存的key
  LZ_CRM_BIZ_USER: 'LZ_CRM_BIZ_USER',
  // 当前storage里面的店铺信息
  LZ_CURRENT_SHOP: 'LZ_CURRENT_SHOP',
  // sso 过期通信
  LZ_EVENT_SSO_TOKEN_EXPIRE: 'LZ_EVENT_SSO_TOKEN_EXPIRE',
  // 登录页面地址
  LZ_LOGIN_PAGE_URL: '/login',
  // 成功返回值code
  LZ_SUCCESS_CODE: 200,
  // 统一服务端错误消息
  LZ_SERVER_ERROR_MESSAGE: '服务器请求出错，请刷新页面后重新尝试',
  // 统一身份异常
  LZ_AUTH_ERROR_MESSAGE: '当前登录身份已经过期，请重新登录',
  // 统一格式化模板
  DATE_FORMAT_TEMPLATE: 'YYYY-MM-DD HH:mm:ss',
  // 京东图片空间前缀，pc端暂时使用主图的，这个地方可以按照需要的尺寸
  IMAGE_PREFIX: 'https://img10.360buyimg.com/imgzone/',
  IMAGE_PREFIX_340: 'https://img10.360buyimg.com/n1/',
  IMAGE_PREFIX_160: 'https://img10.360buyimg.com/n2/',
  IMAGE_PREFIX_130: 'https://img10.360buyimg.com/n3/',
  IMAGE_PREFIX_100: 'https://img10.360buyimg.com/n4/',
  IMAGE_PREFIX_50: 'https://img10.360buyimg.com/n5/',
  IMAGE_PREFIX_240: 'https://img10.360buyimg.com/n6/',
  IMAGE_PREFIX_220: 'https://img10.360buyimg.com/n7/',
  IMAGE_PREFIX_N8: 'https://img10.360buyimg.com/n8/',

  PRIZE_TYPE: {
    12: '京元宝权益',
    2: '京豆',
    1: '优惠券',
    3: '实物',
    4: '积分',
    6: '红包',
    7: '礼品卡',
    8: '京东E卡',
    9: 'PLUS会员卡',
    10: '爱奇艺会员卡',
  },
};
